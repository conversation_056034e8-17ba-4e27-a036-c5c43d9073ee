/*
  # Add Email Verification System

  1. Schema Changes
    - Add `email_verified` column to user_profiles table
    - Add `email_verification_token` column for OTP storage
    - Add `email_verification_expires_at` column for token expiration
    - Add `email_verification_attempts` column to track failed attempts

  2. Security
    - Update RLS policies to check email verification status
    - Add rate limiting for verification attempts

  3. Functions
    - Add function to generate and send verification codes
    - Add function to verify OTP codes
*/

-- Add email verification columns to user_profiles
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'user_profiles' AND column_name = 'email_verified'
  ) THEN
    ALTER TABLE user_profiles ADD COLUMN email_verified boolean DEFAULT false;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'user_profiles' AND column_name = 'email_verification_token'
  ) THEN
    ALTER TABLE user_profiles ADD COLUMN email_verification_token text;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'user_profiles' AND column_name = 'email_verification_expires_at'
  ) THEN
    ALTER TABLE user_profiles ADD COLUMN email_verification_expires_at timestamptz;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'user_profiles' AND column_name = 'email_verification_attempts'
  ) THEN
    ALTER TABLE user_profiles ADD COLUMN email_verification_attempts integer DEFAULT 0;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'user_profiles' AND column_name = 'last_verification_attempt'
  ) THEN
    ALTER TABLE user_profiles ADD COLUMN last_verification_attempt timestamptz;
  END IF;
END $$;

-- Create email verification table for tracking verification history
CREATE TABLE IF NOT EXISTS email_verifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  email text NOT NULL,
  verification_code text NOT NULL,
  expires_at timestamptz NOT NULL,
  verified_at timestamptz,
  attempts integer DEFAULT 0,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS on email_verifications
ALTER TABLE email_verifications ENABLE ROW LEVEL SECURITY;

-- Policy for email_verifications
CREATE POLICY "Users can manage own email verifications"
  ON email_verifications
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Function to generate verification code
CREATE OR REPLACE FUNCTION generate_verification_code()
RETURNS text AS $$
BEGIN
  -- Generate a 6-digit numeric code
  RETURN LPAD(FLOOR(RANDOM() * 1000000)::text, 6, '0');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create email verification
CREATE OR REPLACE FUNCTION create_email_verification(user_email text)
RETURNS json AS $$
DECLARE
  user_record auth.users%ROWTYPE;
  verification_code text;
  expires_at timestamptz;
  verification_record email_verifications%ROWTYPE;
BEGIN
  -- Get user by email
  SELECT * INTO user_record FROM auth.users WHERE email = user_email;
  
  IF user_record.id IS NULL THEN
    RETURN json_build_object('success', false, 'error', 'User not found');
  END IF;

  -- Check rate limiting (max 3 attempts per hour)
  IF EXISTS (
    SELECT 1 FROM email_verifications 
    WHERE user_id = user_record.id 
    AND created_at > NOW() - INTERVAL '1 hour'
    AND attempts >= 3
  ) THEN
    RETURN json_build_object('success', false, 'error', 'Too many verification attempts. Please try again later.');
  END IF;

  -- Generate verification code and expiration
  verification_code := generate_verification_code();
  expires_at := NOW() + INTERVAL '10 minutes';

  -- Insert verification record
  INSERT INTO email_verifications (user_id, email, verification_code, expires_at)
  VALUES (user_record.id, user_email, verification_code, expires_at)
  RETURNING * INTO verification_record;

  -- Update user profile with verification token
  UPDATE user_profiles 
  SET 
    email_verification_token = verification_code,
    email_verification_expires_at = expires_at,
    last_verification_attempt = NOW()
  WHERE id = user_record.id;

  -- In a real app, you would send the email here
  -- For now, we'll return the code for testing purposes
  RETURN json_build_object(
    'success', true, 
    'verification_id', verification_record.id,
    'code', verification_code, -- Remove this in production
    'expires_at', expires_at
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to verify email with OTP
CREATE OR REPLACE FUNCTION verify_email_otp(user_email text, otp_code text)
RETURNS json AS $$
DECLARE
  user_record auth.users%ROWTYPE;
  verification_record email_verifications%ROWTYPE;
BEGIN
  -- Get user by email
  SELECT * INTO user_record FROM auth.users WHERE email = user_email;
  
  IF user_record.id IS NULL THEN
    RETURN json_build_object('success', false, 'error', 'User not found');
  END IF;

  -- Get latest verification record
  SELECT * INTO verification_record 
  FROM email_verifications 
  WHERE user_id = user_record.id 
  AND email = user_email
  AND verified_at IS NULL
  ORDER BY created_at DESC 
  LIMIT 1;

  IF verification_record.id IS NULL THEN
    RETURN json_build_object('success', false, 'error', 'No pending verification found');
  END IF;

  -- Check if code has expired
  IF verification_record.expires_at < NOW() THEN
    RETURN json_build_object('success', false, 'error', 'Verification code has expired');
  END IF;

  -- Increment attempts
  UPDATE email_verifications 
  SET attempts = attempts + 1 
  WHERE id = verification_record.id;

  -- Check if too many attempts
  IF verification_record.attempts >= 5 THEN
    RETURN json_build_object('success', false, 'error', 'Too many failed attempts. Please request a new code.');
  END IF;

  -- Verify the code
  IF verification_record.verification_code = otp_code THEN
    -- Mark as verified
    UPDATE email_verifications 
    SET verified_at = NOW() 
    WHERE id = verification_record.id;

    -- Update user profile
    UPDATE user_profiles 
    SET 
      email_verified = true,
      email_verification_token = NULL,
      email_verification_expires_at = NULL,
      email_verification_attempts = 0
    WHERE id = user_record.id;

    RETURN json_build_object('success', true, 'message', 'Email verified successfully');
  ELSE
    RETURN json_build_object('success', false, 'error', 'Invalid verification code');
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the handle_new_user function to set email_verified to false by default
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.user_profiles (id, full_name, email, email_verified)
  VALUES (
    new.id,
    COALESCE(new.raw_user_meta_data->>'full_name', ''),
    COALESCE(new.email, ''),
    false
  );
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update existing user profiles to have email_verified = false if not set
UPDATE user_profiles 
SET email_verified = false 
WHERE email_verified IS NULL;

-- Make email_verified NOT NULL with default false
ALTER TABLE user_profiles ALTER COLUMN email_verified SET NOT NULL;
ALTER TABLE user_profiles ALTER COLUMN email_verified SET DEFAULT false;