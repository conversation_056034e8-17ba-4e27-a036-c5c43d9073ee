import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Modal, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { supabase } from '@/lib/supabase';
import { openRouterService } from '@/lib/openrouter';
import { PersonalityProfile, CircleContact } from '@/types/personality';
import { TEMPERAMENT_COLORS, TEMPERAMENT_DESCRIPTIONS } from '@/constants/temperaments';
import { Bell, Search, Users, TrendingUp, MessageSquare, Clock, TriangleAlert as AlertT<PERSON>gle, CircleCheck as CheckCircle, X, ChevronDown, Lightbulb, Heart, Target, Sparkles, Brain, Shuffle, Save, History } from 'lucide-react-native';

interface ComparisonData {
  id?: string;
  person1_name: string;
  person1_temperament: string;
  person1_percentage: number;
  person2_name: string;
  person2_temperament: string;
  person2_percentage: number;
  compatibility_score: number;
  communication_styles: {
    person1: string;
    person2: string;
    bridgeTip: string;
  };
  potential_conflicts: Array<{
    title: string;
    description: string;
    tip: string;
  }>;
  strengths: string[];
  best_time_to_interact: string;
  relationship_tips: string[];
  ai_insights: string;
  created_at?: string;
}

interface ComparisonHistory {
  id: string;
  comparison_data: ComparisonData;
  created_at: string;
}

export default function CompareScreen() {
  const [userProfile, setUserProfile] = useState<PersonalityProfile | null>(null);
  const [contacts, setContacts] = useState<CircleContact[]>([]);
  const [selectedPerson1, setSelectedPerson1] = useState<CircleContact | PersonalityProfile | null>(null);
  const [selectedPerson2, setSelectedPerson2] = useState<CircleContact | null>(null);
  const [comparisonData, setComparisonData] = useState<ComparisonData | null>(null);
  const [showPerson1Modal, setShowPerson1Modal] = useState(false);
  const [showPerson2Modal, setShowPerson2Modal] = useState(false);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [searchQuery1, setSearchQuery1] = useState('');
  const [searchQuery2, setSearchQuery2] = useState('');
  const [loading, setLoading] = useState(false);
  const [comparisonHistory, setComparisonHistory] = useState<ComparisonHistory[]>([]);
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const mounted = useRef(true);

  useEffect(() => {
    mounted.current = true;
    loadUserData();
    loadComparisonHistory();
    
    return () => {
      mounted.current = false;
    };
  }, []);

  const loadUserData = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user || !mounted.current) return;

      // Load user's personality profile
      const { data: personality } = await supabase
        .from('personality_profiles')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();
      
      if (mounted.current) {
        setUserProfile(personality);
      }

      // Load user's circle contacts with personality assessments
      const { data: circleContacts } = await supabase
        .from('circle_contacts')
        .select('*')
        .eq('user_id', user.id)
        .not('dominant_temperament', 'is', null)
        .order('name');
      
      if (mounted.current) {
        setContacts(circleContacts || []);
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const loadComparisonHistory = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: history } = await supabase
        .from('comparison_history')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(20);

      if (mounted.current && history) {
        setComparisonHistory(history);
      }
    } catch (error) {
      console.error('Error loading comparison history:', error);
    }
  };

  const generateAIInsights = async (person1: any, person2: any): Promise<string> => {
    if (!openRouterService.isConfigured()) {
      return "AI insights are not available. Please configure the OpenRouter API key to get personalized relationship insights.";
    }

    setIsGeneratingAI(true);
    
    try {
      const prompt = `You are a relationship psychology expert. Analyze the personality compatibility between these two people and provide deep insights.

PERSON 1: ${person1.name || 'User'}
- Temperament: ${person1.dominant_temperament} (${person1.dominant_percentage || 100}%)
- Secondary: ${person1.secondary_temperament || 'N/A'}

PERSON 2: ${person2.name}
- Temperament: ${person2.dominant_temperament} (${person2.dominant_percentage || 100}%)
- Secondary: ${person2.secondary_temperament || 'N/A'}

TEMPERAMENT CHARACTERISTICS:
- Choleric: Natural leaders, decisive, goal-oriented, direct, ambitious, can be impatient
- Sanguine: Enthusiastic, social, optimistic, spontaneous, people-focused, can be disorganized
- Melancholic: Thoughtful, analytical, creative, introspective, detail-oriented, can be pessimistic
- Phlegmatic: Calm, peaceful, supportive, diplomatic, steady, can be passive

Provide a comprehensive analysis covering:
1. Core compatibility dynamics
2. How their temperaments complement each other
3. Potential growth opportunities for both
4. Specific communication strategies
5. How they can support each other's weaknesses
6. Long-term relationship sustainability

Keep the response conversational, insightful, and actionable. Focus on practical advice they can use immediately.`;

      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.EXPO_PUBLIC_OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://harmona.app',
          'X-Title': 'Harmona Personality Comparison'
        },
        body: JSON.stringify({
          model: 'google/gemini-2.5-flash-preview-05-20',
          messages: [
            {
              role: 'system',
              content: 'You are an expert relationship psychologist specializing in temperament-based compatibility analysis. Provide deep, actionable insights.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.8,
          max_tokens: 1500,
          top_p: 0.9
        })
      });

      if (!response.ok) {
        throw new Error(`AI service error: ${response.status}`);
      }

      const data = await response.json();
      const aiInsights = data.choices[0]?.message?.content;

      if (!aiInsights) {
        throw new Error('No insights received from AI service');
      }

      return aiInsights;

    } catch (error) {
      console.error('Error generating AI insights:', error);
      return "Unable to generate AI insights at this time. Please try again later.";
    } finally {
      setIsGeneratingAI(false);
    }
  };

  const handleCompare = async () => {
    if (!selectedPerson1 || !selectedPerson2) {
      Alert.alert('Error', 'Please select two people to compare');
      return;
    }

    setLoading(true);

    try {
      // Generate AI insights
      const aiInsights = await generateAIInsights(selectedPerson1, selectedPerson2);

      // Generate comparison data
      const comparison = await generateComparisonData(selectedPerson1, selectedPerson2, aiInsights);
      
      if (mounted.current) {
        setComparisonData(comparison);
        
        // Save to database
        await saveComparisonToDatabase(comparison);
        
        // Reload history
        loadComparisonHistory();
      }
    } catch (error) {
      console.error('Error generating comparison:', error);
      Alert.alert('Error', 'Failed to generate comparison. Please try again.');
    } finally {
      if (mounted.current) {
        setLoading(false);
      }
    }
  };

  const generateComparisonData = async (person1: any, person2: any, aiInsights: string): Promise<ComparisonData> => {
    const person1Temp = person1.dominant_temperament;
    const person2Temp = person2.dominant_temperament;
    const person1Name = person1.name || 'You';
    const person2Name = person2.name;

    // Calculate compatibility score
    const compatibilityScore = calculateCompatibility(person1Temp, person2Temp);

    // Generate communication styles
    const communicationStyles = getCommunicationStyles(person1Temp, person2Temp);

    // Generate potential conflicts
    const potentialConflicts = getPotentialConflicts(person1Temp, person2Temp);

    // Generate strengths
    const strengths = getRelationshipStrengths(person1Temp, person2Temp);

    // Generate best interaction time
    const bestTimeToInteract = getBestInteractionTime(person1Temp, person2Temp);

    // Generate relationship tips
    const relationshipTips = getRelationshipTips(person1Temp, person2Temp);

    return {
      person1_name: person1Name,
      person1_temperament: person1Temp,
      person1_percentage: person1.dominant_percentage || 100,
      person2_name: person2Name,
      person2_temperament: person2Temp,
      person2_percentage: person2.dominant_percentage || 100,
      compatibility_score: compatibilityScore,
      communication_styles: communicationStyles,
      potential_conflicts: potentialConflicts,
      strengths,
      best_time_to_interact: bestTimeToInteract,
      relationship_tips: relationshipTips,
      ai_insights: aiInsights
    };
  };

  const saveComparisonToDatabase = async (comparison: ComparisonData) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { error } = await supabase
        .from('comparison_history')
        .insert({
          user_id: user.id,
          comparison_data: comparison
        });

      if (error) throw error;
    } catch (error) {
      console.error('Error saving comparison to database:', error);
    }
  };

  const calculateCompatibility = (temp1: string, temp2: string): number => {
    const compatibilityMatrix: { [key: string]: number } = {
      'choleric-sanguine': 85,
      'choleric-melancholic': 70,
      'choleric-phlegmatic': 60,
      'sanguine-melancholic': 65,
      'sanguine-phlegmatic': 80,
      'melancholic-phlegmatic': 75,
      'choleric-choleric': 55,
      'sanguine-sanguine': 90,
      'melancholic-melancholic': 80,
      'phlegmatic-phlegmatic': 85
    };

    const key1 = `${temp1}-${temp2}`;
    const key2 = `${temp2}-${temp1}`;
    
    return compatibilityMatrix[key1] || compatibilityMatrix[key2] || 70;
  };

  const getCommunicationStyles = (temp1: string, temp2: string) => {
    const styles: { [key: string]: string } = {
      choleric: 'Direct and goal-focused',
      sanguine: 'Enthusiastic and expressive',
      melancholic: 'Thoughtful and detailed',
      phlegmatic: 'Calm and diplomatic'
    };

    return {
      person1: styles[temp1] || 'Balanced',
      person2: styles[temp2] || 'Adaptable',
      bridgeTip: 'Find a middle ground by combining directness with empathy, and structure with flexibility.'
    };
  };

  const getPotentialConflicts = (temp1: string, temp2: string) => {
    const conflictMap: { [key: string]: any } = {
      'choleric-phlegmatic': {
        title: 'Decision-making Speed',
        description: 'One prefers quick decisions while the other takes time to consider all options.',
        tip: 'Set clear deadlines for decisions and explain reasoning. Give time to process when possible.'
      },
      'sanguine-melancholic': {
        title: 'Planning vs Spontaneity',
        description: 'One loves spontaneous activities while the other prefers detailed planning.',
        tip: 'Plan some activities in advance while leaving room for spontaneous moments.'
      },
      'choleric-sanguine': {
        title: 'Focus vs Flexibility',
        description: 'One focuses intensely on goals while the other gets distracted by new opportunities.',
        tip: 'Break goals into smaller, engaging milestones and celebrate progress together.'
      }
    };

    const key1 = `${temp1}-${temp2}`;
    const key2 = `${temp2}-${temp1}`;
    
    return [conflictMap[key1] || conflictMap[key2] || {
      title: 'Communication Differences',
      description: 'Different temperaments may have varying communication preferences.',
      tip: 'Practice active listening and adapt communication styles to match preferences.'
    }];
  };

  const getRelationshipStrengths = (temp1: string, temp2: string): string[] => {
    const strengthsMap: { [key: string]: string[] } = {
      'choleric-sanguine': ['Dynamic energy', 'Goal achievement', 'Social leadership'],
      'choleric-melancholic': ['Strategic thinking', 'Quality execution', 'Balanced perspective'],
      'choleric-phlegmatic': ['Stable leadership', 'Calm decision-making', 'Balanced approach'],
      'sanguine-melancholic': ['Creative collaboration', 'Emotional depth', 'Balanced social energy'],
      'sanguine-phlegmatic': ['Harmonious interaction', 'Supportive communication', 'Peaceful energy'],
      'melancholic-phlegmatic': ['Thoughtful planning', 'Stable execution', 'Deep understanding']
    };

    const key1 = `${temp1}-${temp2}`;
    const key2 = `${temp2}-${temp1}`;
    
    return strengthsMap[key1] || strengthsMap[key2] || ['Complementary perspectives', 'Mutual growth', 'Balanced dynamics'];
  };

  const getBestInteractionTime = (temp1: string, temp2: string): string => {
    const preferences: { [key: string]: string } = {
      choleric: 'morning',
      sanguine: 'afternoon',
      melancholic: 'evening',
      phlegmatic: 'any time'
    };

    const pref1 = preferences[temp1];
    const pref2 = preferences[temp2];

    if (pref1 === pref2) {
      return `Both are most productive in the ${pref1}. Schedule important interactions then.`;
    }

    return `One prefers ${pref1} while the other prefers ${pref2}. Consider mid-day as a compromise.`;
  };

  const getRelationshipTips = (temp1: string, temp2: string): string[] => {
    return [
      'Practice active listening and show genuine interest in each other\'s perspectives',
      'Acknowledge and appreciate your different strengths and approaches',
      'Create space for both structured planning and spontaneous activities',
      'Communicate your needs clearly and ask about theirs regularly',
      'Focus on shared values and goals while respecting different methods'
    ];
  };

  const formatTemperamentName = (temperament: string | null | undefined): string => {
    if (!temperament || temperament.trim() === '') {
      return 'Unknown';
    }
    return temperament.charAt(0).toUpperCase() + temperament.slice(1);
  };

  const getTemperamentColor = (temperament: string) => {
    return TEMPERAMENT_COLORS[temperament as keyof typeof TEMPERAMENT_COLORS] || '#6B7280';
  };

  const filteredContacts1 = contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchQuery1.toLowerCase())
  );

  const filteredContacts2 = contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchQuery2.toLowerCase()) &&
    contact.id !== selectedPerson1?.id
  );

  const allPeople1 = userProfile ? [userProfile, ...contacts] : contacts;
  const filteredPeople1 = allPeople1.filter(person => {
    const name = 'name' in person ? person.name : 'You';
    return name.toLowerCase().includes(searchQuery1.toLowerCase());
  });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.title}>Personality Compare</Text>
            <Text style={styles.subtitle}>
              Analyze compatibility between any two people in your circle
            </Text>
          </View>
          <TouchableOpacity
            style={styles.historyButton}
            onPress={() => setShowHistoryModal(true)}
          >
            <History size={20} color="#3B82F6" />
          </TouchableOpacity>
        </View>

        {/* Selection Area */}
        <View style={styles.selectionArea}>
          <Text style={styles.sectionTitle}>Select People to Compare</Text>
          
          <View style={styles.selectionRow}>
            <TouchableOpacity
              style={styles.personSelector}
              onPress={() => setShowPerson1Modal(true)}
            >
              <Users size={20} color="#6B7280" />
              <Text style={styles.personSelectorText}>
                {selectedPerson1 ? ('name' in selectedPerson1 ? selectedPerson1.name : 'You') : 'Select first person'}
              </Text>
              <ChevronDown size={20} color="#6B7280" />
            </TouchableOpacity>

            <View style={styles.vsContainer}>
              <Text style={styles.vsText}>VS</Text>
            </View>

            <TouchableOpacity
              style={styles.personSelector}
              onPress={() => setShowPerson2Modal(true)}
            >
              <Users size={20} color="#6B7280" />
              <Text style={styles.personSelectorText}>
                {selectedPerson2 ? selectedPerson2.name : 'Select second person'}
              </Text>
              <ChevronDown size={20} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[
              styles.compareButton,
              (!selectedPerson1 || !selectedPerson2 || loading) && styles.compareButtonDisabled
            ]}
            onPress={handleCompare}
            disabled={!selectedPerson1 || !selectedPerson2 || loading}
          >
            {isGeneratingAI ? (
              <Sparkles size={20} color="#ffffff" />
            ) : (
              <Brain size={20} color="#ffffff" />
            )}
            <Text style={styles.compareButtonText}>
              {loading ? (isGeneratingAI ? 'Generating AI Insights...' : 'Analyzing...') : 'Compare Personalities'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Comparison Results */}
        {selectedPerson1 && selectedPerson2 && comparisonData && !loading && (
          <>
            {/* Personality Comparison */}
            <View style={styles.comparisonArea}>
              <Text style={styles.sectionTitle}>Personality Comparison</Text>
              <View style={styles.comparisonCard}>
                <View style={styles.personalityComparison}>
                  <View style={styles.personalityColumn}>
                    <Text style={styles.personalityLabel}>
                      {comparisonData.person1_name}
                    </Text>
                    <View style={styles.temperamentInfo}>
                      <View 
                        style={[
                          styles.temperamentIndicator, 
                          { backgroundColor: getTemperamentColor(comparisonData.person1_temperament) }
                        ]} 
                      />
                      <Text style={styles.temperamentName}>
                        {formatTemperamentName(comparisonData.person1_temperament)}
                      </Text>
                    </View>
                    <Text style={styles.temperamentPercentage}>{comparisonData.person1_percentage}%</Text>
                  </View>

                  <View style={styles.vsContainer}>
                    <Text style={styles.vsText}>VS</Text>
                    <View style={styles.compatibilityScore}>
                      <Text style={styles.compatibilityText}>{comparisonData.compatibility_score}%</Text>
                      <Text style={styles.compatibilityLabel}>Compatible</Text>
                    </View>
                  </View>

                  <View style={styles.personalityColumn}>
                    <Text style={styles.personalityLabel}>
                      {comparisonData.person2_name}
                    </Text>
                    <View style={styles.temperamentInfo}>
                      <View 
                        style={[
                          styles.temperamentIndicator, 
                          { backgroundColor: getTemperamentColor(comparisonData.person2_temperament) }
                        ]} 
                      />
                      <Text style={styles.temperamentName}>
                        {formatTemperamentName(comparisonData.person2_temperament)}
                      </Text>
                    </View>
                    <Text style={styles.temperamentPercentage}>{comparisonData.person2_percentage}%</Text>
                  </View>
                </View>
              </View>
            </View>

            {/* AI Insights */}
            <View style={styles.aiInsightsArea}>
              <View style={styles.aiInsightsHeader}>
                <Sparkles size={20} color="#8B5CF6" />
                <Text style={styles.sectionTitle}>AI Relationship Insights</Text>
              </View>
              <View style={styles.aiInsightsCard}>
                <Text style={styles.aiInsightsText}>{comparisonData.ai_insights}</Text>
              </View>
            </View>

            {/* Relationship Strengths */}
            <View style={styles.strengthsArea}>
              <Text style={styles.sectionTitle}>Relationship Strengths</Text>
              {comparisonData.strengths.map((strength, index) => (
                <View key={index} style={styles.strengthCard}>
                  <CheckCircle size={20} color="#10B981" />
                  <Text style={styles.strengthText}>{strength}</Text>
                </View>
              ))}
            </View>

            {/* Potential Conflicts */}
            <View style={styles.conflictsArea}>
              <Text style={styles.sectionTitle}>Potential Challenges</Text>
              {comparisonData.potential_conflicts.map((conflict, index) => (
                <View key={index} style={styles.conflictCard}>
                  <View style={styles.conflictHeader}>
                    <AlertTriangle size={20} color="#EF4444" />
                    <Text style={styles.conflictTitle}>{conflict.title}</Text>
                  </View>
                  <Text style={styles.conflictDescription}>{conflict.description}</Text>
                  <View style={styles.conflictTip}>
                    <Lightbulb size={16} color="#10B981" />
                    <Text style={styles.conflictTipText}>{conflict.tip}</Text>
                  </View>
                </View>
              ))}
            </View>

            {/* Communication Styles */}
            <View style={styles.communicationArea}>
              <Text style={styles.sectionTitle}>Communication Styles</Text>
              <View style={styles.communicationCard}>
                <View style={styles.communicationRow}>
                  <Text style={styles.communicationLabel}>{comparisonData.person1_name}:</Text>
                  <Text style={styles.communicationStyle}>{comparisonData.communication_styles.person1}</Text>
                </View>
                <View style={styles.communicationRow}>
                  <Text style={styles.communicationLabel}>{comparisonData.person2_name}:</Text>
                  <Text style={styles.communicationStyle}>{comparisonData.communication_styles.person2}</Text>
                </View>
                <View style={styles.bridgeTip}>
                  <MessageSquare size={16} color="#3B82F6" />
                  <Text style={styles.bridgeTipText}>{comparisonData.communication_styles.bridgeTip}</Text>
                </View>
              </View>
            </View>

            {/* Best Time to Interact */}
            <View style={styles.timingArea}>
              <Text style={styles.sectionTitle}>Best Time to Interact</Text>
              <View style={styles.timingCard}>
                <Clock size={20} color="#8B5CF6" />
                <Text style={styles.timingText}>{comparisonData.best_time_to_interact}</Text>
              </View>
            </View>

            {/* Relationship Tips */}
            <View style={styles.tipsArea}>
              <Text style={styles.sectionTitle}>Relationship Tips</Text>
              {comparisonData.relationship_tips.map((tip, index) => (
                <View key={index} style={styles.tipCard}>
                  <Heart size={20} color="#EF4444" />
                  <Text style={styles.tipText}>{tip}</Text>
                </View>
              ))}
            </View>
          </>
        )}

        {loading && (
          <View style={styles.loadingContainer}>
            <Brain size={48} color="#3B82F6" />
            <Text style={styles.loadingText}>
              {isGeneratingAI ? 'Generating AI insights...' : 'Analyzing personality compatibility...'}
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Person 1 Selection Modal */}
      <Modal
        visible={showPerson1Modal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowPerson1Modal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select First Person</Text>
              <TouchableOpacity onPress={() => setShowPerson1Modal(false)}>
                <X size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <View style={styles.searchContainer}>
              <Search size={20} color="#6B7280" />
              <TextInput
                style={styles.searchInput}
                placeholder="Search people..."
                value={searchQuery1}
                onChangeText={setSearchQuery1}
              />
            </View>

            <ScrollView style={styles.contactsList}>
              {filteredPeople1.map((person) => {
                const isUser = !('name' in person);
                const name = isUser ? 'You' : person.name;
                const temperament = person.dominant_temperament;
                
                return (
                  <TouchableOpacity
                    key={isUser ? 'user' : person.id}
                    style={styles.contactItem}
                    onPress={() => {
                      setSelectedPerson1(person);
                      setShowPerson1Modal(false);
                    }}
                  >
                    <View style={styles.contactInfo}>
                      <Text style={styles.contactName}>{name}</Text>
                      {!isUser && (
                        <Text style={styles.contactCategory}>{person.category}</Text>
                      )}
                    </View>
                    {temperament && (
                      <View style={styles.contactTemperament}>
                        <View 
                          style={[
                            styles.temperamentDot, 
                            { backgroundColor: getTemperamentColor(temperament) }
                          ]} 
                        />
                        <Text style={styles.contactTemperamentText}>
                          {formatTemperamentName(temperament)}
                        </Text>
                      </View>
                    )}
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Person 2 Selection Modal */}
      <Modal
        visible={showPerson2Modal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowPerson2Modal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Second Person</Text>
              <TouchableOpacity onPress={() => setShowPerson2Modal(false)}>
                <X size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <View style={styles.searchContainer}>
              <Search size={20} color="#6B7280" />
              <TextInput
                style={styles.searchInput}
                placeholder="Search contacts..."
                value={searchQuery2}
                onChangeText={setSearchQuery2}
              />
            </View>

            <ScrollView style={styles.contactsList}>
              {filteredContacts2.map((contact) => (
                <TouchableOpacity
                  key={contact.id}
                  style={styles.contactItem}
                  onPress={() => {
                    setSelectedPerson2(contact);
                    setShowPerson2Modal(false);
                  }}
                >
                  <View style={styles.contactInfo}>
                    <Text style={styles.contactName}>{contact.name}</Text>
                    <Text style={styles.contactCategory}>{contact.category}</Text>
                  </View>
                  {contact.dominant_temperament && (
                    <View style={styles.contactTemperament}>
                      <View 
                        style={[
                          styles.temperamentDot, 
                          { backgroundColor: getTemperamentColor(contact.dominant_temperament) }
                        ]} 
                      />
                      <Text style={styles.contactTemperamentText}>
                        {formatTemperamentName(contact.dominant_temperament)}
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Comparison History Modal */}
      <Modal
        visible={showHistoryModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowHistoryModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Comparison History</Text>
              <TouchableOpacity onPress={() => setShowHistoryModal(false)}>
                <X size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.historyList}>
              {comparisonHistory.length === 0 ? (
                <View style={styles.emptyHistory}>
                  <History size={48} color="#D1D5DB" />
                  <Text style={styles.emptyHistoryTitle}>No comparisons yet</Text>
                  <Text style={styles.emptyHistoryText}>
                    Start comparing personalities to see your history here
                  </Text>
                </View>
              ) : (
                comparisonHistory.map((item) => (
                  <TouchableOpacity
                    key={item.id}
                    style={styles.historyItem}
                    onPress={() => {
                      setComparisonData(item.comparison_data);
                      setShowHistoryModal(false);
                    }}
                  >
                    <View style={styles.historyItemHeader}>
                      <Text style={styles.historyItemTitle}>
                        {item.comparison_data.person1_name} vs {item.comparison_data.person2_name}
                      </Text>
                      <Text style={styles.historyItemDate}>
                        {new Date(item.created_at).toLocaleDateString()}
                      </Text>
                    </View>
                    <View style={styles.historyItemDetails}>
                      <View style={styles.historyTemperaments}>
                        <Text style={styles.historyTemperament}>
                          {formatTemperamentName(item.comparison_data.person1_temperament)}
                        </Text>
                        <Text style={styles.historyVs}>vs</Text>
                        <Text style={styles.historyTemperament}>
                          {formatTemperamentName(item.comparison_data.person2_temperament)}
                        </Text>
                      </View>
                      <Text style={styles.historyCompatibility}>
                        {item.comparison_data.compatibility_score}% compatible
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  historyButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#EBF8FF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectionArea: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  selectionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 20,
  },
  personSelector: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  personSelectorText: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
  },
  vsContainer: {
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  vsText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#9CA3AF',
  },
  compareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3B82F6',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  compareButtonDisabled: {
    opacity: 0.5,
  },
  compareButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  comparisonArea: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  comparisonCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  personalityComparison: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  personalityColumn: {
    alignItems: 'center',
    flex: 1,
  },
  personalityLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
    textAlign: 'center',
  },
  temperamentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  temperamentIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  temperamentName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  temperamentPercentage: {
    fontSize: 14,
    color: '#6B7280',
  },
  compatibilityScore: {
    alignItems: 'center',
    backgroundColor: '#F0FDF4',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  compatibilityText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#16A34A',
  },
  compatibilityLabel: {
    fontSize: 10,
    color: '#16A34A',
  },
  aiInsightsArea: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  aiInsightsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  aiInsightsCard: {
    backgroundColor: '#F8F4FF',
    borderRadius: 12,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E9D5FF',
  },
  aiInsightsText: {
    fontSize: 14,
    color: '#6B46C1',
    lineHeight: 22,
  },
  strengthsArea: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  strengthCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    gap: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  strengthText: {
    flex: 1,
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
  },
  conflictsArea: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  conflictCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  conflictHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  conflictTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  conflictDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 12,
  },
  conflictTip: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#F0FDF4',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  conflictTipText: {
    flex: 1,
    fontSize: 14,
    color: '#16A34A',
    lineHeight: 20,
  },
  communicationArea: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  communicationCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  communicationRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  communicationLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
    width: 80,
  },
  communicationStyle: {
    flex: 1,
    fontSize: 14,
    color: '#6B7280',
  },
  bridgeTip: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#EBF8FF',
    padding: 12,
    borderRadius: 8,
    gap: 8,
    marginTop: 8,
  },
  bridgeTipText: {
    flex: 1,
    fontSize: 14,
    color: '#1E40AF',
    lineHeight: 20,
  },
  timingArea: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  timingCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    gap: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  timingText: {
    flex: 1,
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
  },
  tipsArea: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  tipCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    gap: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
  },
  loadingContainer: {
    paddingVertical: 60,
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 40,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    marginBottom: 20,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
  },
  contactsList: {
    maxHeight: 400,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  contactCategory: {
    fontSize: 14,
    color: '#6B7280',
    textTransform: 'capitalize',
  },
  contactTemperament: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  temperamentDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  contactTemperamentText: {
    fontSize: 12,
    color: '#6B7280',
  },
  historyList: {
    maxHeight: 500,
  },
  emptyHistory: {
    alignItems: 'center',
    paddingVertical: 40,
    gap: 12,
  },
  emptyHistoryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B7280',
  },
  emptyHistoryText: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 20,
  },
  historyItem: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  historyItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  historyItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    flex: 1,
  },
  historyItemDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  historyItemDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  historyTemperaments: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  historyTemperament: {
    fontSize: 14,
    color: '#6B7280',
  },
  historyVs: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  historyCompatibility: {
    fontSize: 14,
    fontWeight: '600',
    color: '#10B981',
  },
});