import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  supabase,
  isSupabaseConfigured,
  getDemoCredentials,
} from '../lib/supabase';
import { useAuthStore } from './authStore';
import { useProfileStore } from './profileStore';
import { TemperamentType } from '../types/personality';

// Define the types for contact category
export type ContactCategory = 'family' | 'friends' | 'work' | 'other';

// Define the structure for a contact in the circle
export interface Contact {
  id: string;
  user_id: string;
  name: string;
  primary_temperament: TemperamentType | null;
  secondary_temperament: TemperamentType | null;
  category: ContactCategory;
  notes: string | null;
  compatibility_score: number | null;
  created_at: string;
  updated_at: string;
}

// Define a type for contact creation/update
export interface ContactInput {
  name: string;
  primary_temperament?: TemperamentType | null;
  secondary_temperament?: TemperamentType | null;
  category: ContactCategory;
  notes?: string | null;
}

// Compatibility calculation constants
const COMPATIBILITY_MATRIX: Record<
  TemperamentType,
  Record<TemperamentType, number>
> = {
  choleric: {
    choleric: 60, // Same temperament compatibility is moderate
    sanguine: 80, // Complementary temperament
    melancholic: 70, // Adjacent temperament
    phlegmatic: 50, // Opposite temperament
  },
  sanguine: {
    choleric: 80, // Complementary temperament
    sanguine: 60, // Same temperament compatibility is moderate
    melancholic: 50, // Opposite temperament
    phlegmatic: 70, // Adjacent temperament
  },
  melancholic: {
    choleric: 70, // Adjacent temperament
    sanguine: 50, // Opposite temperament
    melancholic: 60, // Same temperament compatibility is moderate
    phlegmatic: 80, // Complementary temperament
  },
  phlegmatic: {
    choleric: 50, // Opposite temperament
    sanguine: 70, // Adjacent temperament
    melancholic: 80, // Complementary temperament
    phlegmatic: 60, // Same temperament compatibility is moderate
  },
};

// Helper function to calculate compatibility between two people
export function calculateCompatibility(
  person1Primary: TemperamentType,
  person1Secondary: TemperamentType | null,
  person2Primary: TemperamentType | null,
  person2Secondary: TemperamentType | null
): number {
  // If no temperament data for person2, return neutral score
  if (!person2Primary) {
    return 50;
  }

  // Primary-to-primary compatibility (highest weight)
  const primaryScore = COMPATIBILITY_MATRIX[person1Primary][person2Primary];
  let score = primaryScore * 0.6; // 60% weight

  // If both have secondary temperaments, factor those in
  if (person1Secondary && person2Secondary) {
    // Secondary-to-secondary compatibility
    const secondaryScore =
      COMPATIBILITY_MATRIX[person1Secondary][person2Secondary];
    score += secondaryScore * 0.2; // 20% weight

    // Cross compatibility (primary-to-secondary & secondary-to-primary)
    const crossScore1 = COMPATIBILITY_MATRIX[person1Primary][person2Secondary];
    const crossScore2 = COMPATIBILITY_MATRIX[person1Secondary][person2Primary];
    score += (crossScore1 + crossScore2) * 0.1; // 10% weight each
  } else if (person1Secondary) {
    // Only person1 has secondary
    const crossScore = COMPATIBILITY_MATRIX[person1Secondary][person2Primary];
    score += crossScore * 0.4; // 40% weight
  } else if (person2Secondary) {
    // Only person2 has secondary
    const crossScore = COMPATIBILITY_MATRIX[person1Primary][person2Secondary];
    score += crossScore * 0.4; // 40% weight
  }

  // Round to nearest integer
  return Math.round(score);
}

// Generate realistic demo data
const generateDemoContacts = (): Contact[] => {
  const categories: ContactCategory[] = ['family', 'friends', 'work', 'other'];
  const temperaments: TemperamentType[] = [
    'choleric',
    'sanguine',
    'melancholic',
    'phlegmatic',
  ];
  const familyNames = [
    'Mom',
    'Dad',
    'Sister',
    'Brother',
    'Cousin',
    'Aunt',
    'Uncle',
  ];
  const friendNames = [
    'Alex',
    'Taylor',
    'Jordan',
    'Riley',
    'Jamie',
    'Casey',
    'Morgan',
  ];
  const workNames = [
    'Boss',
    'Team Lead',
    'Coworker',
    'Assistant',
    'Manager',
    'Client',
    'Intern',
  ];
  const otherNames = [
    'Neighbor',
    'Doctor',
    'Teacher',
    'Mentor',
    'Barista',
    'Trainer',
    'Landlord',
  ];

  // Get demo user ID
  const { id: userId } = getDemoCredentials();

  // Helper to get random category-appropriate name
  const getNameByCategory = (category: ContactCategory): string => {
    switch (category) {
      case 'family':
        return familyNames[Math.floor(Math.random() * familyNames.length)];
      case 'friends':
        return friendNames[Math.floor(Math.random() * friendNames.length)];
      case 'work':
        return workNames[Math.floor(Math.random() * workNames.length)];
      case 'other':
        return otherNames[Math.floor(Math.random() * otherNames.length)];
    }
  };

  // Get user's own temperament from profile store for compatibility calculation
  const { primaryTemperament, secondaryTemperament } =
    useProfileStore.getState();

  // Generate 10-15 contacts
  const contactCount = Math.floor(Math.random() * 6) + 10;
  const contacts: Contact[] = [];

  for (let i = 0; i < contactCount; i++) {
    // Randomly select category and temperaments
    const category = categories[Math.floor(Math.random() * categories.length)];
    const hasPrimary = Math.random() > 0.2; // 80% chance of having primary temperament
    const hasSecondary = hasPrimary && Math.random() > 0.3; // 70% chance of having secondary if has primary

    const primaryTemp = hasPrimary
      ? temperaments[Math.floor(Math.random() * temperaments.length)]
      : null;

    // Ensure secondary is different from primary
    let secondaryTemp = null;
    if (hasSecondary && primaryTemp) {
      const remainingTemps = temperaments.filter((t) => t !== primaryTemp);
      secondaryTemp =
        remainingTemps[Math.floor(Math.random() * remainingTemps.length)];
    }

    // Calculate compatibility if we have temperament data
    const compatibilityScore =
      primaryTemperament && primaryTemp
        ? calculateCompatibility(
            primaryTemperament,
            secondaryTemperament || null,
            primaryTemp,
            secondaryTemp
          )
        : null;

    const contact: Contact = {
      id: `demo-${i}-${Date.now()}`,
      user_id: userId,
      name: getNameByCategory(category),
      primary_temperament: primaryTemp,
      secondary_temperament: secondaryTemp,
      category,
      notes:
        Math.random() > 0.5
          ? `Some notes about ${getNameByCategory(category)}`
          : null,
      compatibility_score: compatibilityScore,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    contacts.push(contact);
  }

  return contacts;
};

// Define the circle store interface
interface CircleState {
  // State
  contacts: Contact[];
  filteredContacts: Contact[];
  selectedContact: Contact | null;
  activeFilter: ContactCategory | 'all';
  activeSort: 'name' | 'compatibility' | 'category' | 'recent';
  isLoading: boolean;
  isUpdating: boolean;
  error: string | null;
  isDemo: boolean;

  // Actions
  loadContacts: () => Promise<void>;
  getContactById: (id: string) => Contact | null;
  addContact: (contact: ContactInput) => Promise<Contact | null>;
  updateContact: (
    id: string,
    updates: Partial<ContactInput>
  ) => Promise<boolean>;
  deleteContact: (id: string) => Promise<boolean>;
  setSelectedContact: (contact: Contact | null) => void;
  filterContacts: (category: ContactCategory | 'all') => void;
  sortContacts: (
    sortBy: 'name' | 'compatibility' | 'category' | 'recent'
  ) => void;
  searchContacts: (query: string) => void;
  refreshCompatibilityScores: () => Promise<void>;
  clearError: () => void;
  loadDemoContacts: () => void;
}

export const useCircleStore = create<CircleState>()(
  persist(
    (set, get) => ({
      // Initial state
      contacts: [],
      filteredContacts: [],
      selectedContact: null,
      activeFilter: 'all',
      activeSort: 'name',
      isLoading: false,
      isUpdating: false,
      error: null,
      isDemo: false,

      // Load all contacts for the current user
      loadContacts: async () => {
        try {
          set({ isLoading: true, error: null });

          // Check if user is authenticated or in demo mode
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            get().loadDemoContacts();
            return;
          }

          const {
            data: { user },
          } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('User not authenticated');
          }

          // Fetch contacts from the database
          const { data: contacts, error } = await supabase
            .from('contacts')
            .select('*')
            .eq('user_id', user.id)
            .order('name');

          if (error) {
            throw error;
          }

          // Ensure all contacts have correct typing
          const typedContacts = contacts as Contact[];

          set({
            contacts: typedContacts,
            filteredContacts: typedContacts,
            isLoading: false,
          });

          // Recalculate compatibility scores if user profile has changed
          await get().refreshCompatibilityScores();
        } catch (error: any) {
          console.error('Error loading contacts:', error);
          set({
            error: error.message || 'Failed to load contacts',
            isLoading: false,
          });
        }
      },

      // Get a specific contact by ID
      getContactById: (id: string) => {
        return get().contacts.find((contact) => contact.id === id) || null;
      },

      // Add a new contact
      addContact: async (contactInput: ContactInput) => {
        try {
          set({ isUpdating: true, error: null });

          // In demo mode, add to local state only
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            const newContact: Contact = {
              id: `demo-${Date.now()}`,
              user_id: getDemoCredentials().id,
              name: contactInput.name,
              primary_temperament: contactInput.primary_temperament || null,
              secondary_temperament: contactInput.secondary_temperament || null,
              category: contactInput.category,
              notes: contactInput.notes || null,
              compatibility_score: null, // Will be calculated in next step
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            };

            // Calculate compatibility
            if (newContact.primary_temperament) {
              const { primaryTemperament, secondaryTemperament } =
                useProfileStore.getState();
              if (primaryTemperament) {
                newContact.compatibility_score = calculateCompatibility(
                  primaryTemperament,
                  secondaryTemperament || null,
                  newContact.primary_temperament,
                  newContact.secondary_temperament
                );
              }
            }

            // Add to state
            const updatedContacts = [...get().contacts, newContact];
            set({
              contacts: updatedContacts,
              filteredContacts: get().applyFiltersAndSort(updatedContacts),
              isUpdating: false,
            });

            return newContact;
          }

          // Get current user
          const {
            data: { user },
          } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('User not authenticated');
          }

          // Create new contact
          const { data: newContact, error } = await supabase
            .from('contacts')
            .insert({
              user_id: user.id,
              name: contactInput.name,
              primary_temperament: contactInput.primary_temperament || null,
              secondary_temperament: contactInput.secondary_temperament || null,
              category: contactInput.category,
              notes: contactInput.notes || null,
            })
            .select()
            .single();

          if (error) {
            throw error;
          }

          // Calculate compatibility score if possible
          let compatibilityScore = null;
          if (newContact.primary_temperament) {
            const { data: userProfile } = await supabase
              .from('personality_profiles')
              .select('dominant_temperament, secondary_temperament')
              .eq('user_id', user.id)
              .single();

            if (userProfile) {
              compatibilityScore = calculateCompatibility(
                userProfile.dominant_temperament,
                userProfile.secondary_temperament,
                newContact.primary_temperament,
                newContact.secondary_temperament
              );

              // Update the contact with the compatibility score
              await supabase
                .from('contacts')
                .update({ compatibility_score: compatibilityScore })
                .eq('id', newContact.id);

              // Update the local contact object
              newContact.compatibility_score = compatibilityScore;
            }
          }

          // Add to state
          const updatedContacts = [...get().contacts, newContact];
          set({
            contacts: updatedContacts,
            filteredContacts: get().applyFiltersAndSort(updatedContacts),
            isUpdating: false,
          });

          return newContact;
        } catch (error: any) {
          console.error('Error adding contact:', error);
          set({
            error: error.message || 'Failed to add contact',
            isUpdating: false,
          });
          return null;
        }
      },

      // Update an existing contact
      updateContact: async (id: string, updates: Partial<ContactInput>) => {
        try {
          set({ isUpdating: true, error: null });

          // In demo mode, update local state only
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            const updatedContacts = get().contacts.map((contact) => {
              if (contact.id === id) {
                // Create updated contact
                const updatedContact: Contact = {
                  ...contact,
                  ...updates,
                  updated_at: new Date().toISOString(),
                };

                // Recalculate compatibility if temperament changed
                if (
                  updates.primary_temperament !== undefined ||
                  updates.secondary_temperament !== undefined
                ) {
                  const { primaryTemperament, secondaryTemperament } =
                    useProfileStore.getState();
                  if (
                    primaryTemperament &&
                    updatedContact.primary_temperament
                  ) {
                    updatedContact.compatibility_score = calculateCompatibility(
                      primaryTemperament,
                      secondaryTemperament || null,
                      updatedContact.primary_temperament,
                      updatedContact.secondary_temperament
                    );
                  }
                }

                return updatedContact;
              }
              return contact;
            });

            set({
              contacts: updatedContacts,
              filteredContacts: get().applyFiltersAndSort(updatedContacts),
              selectedContact:
                get().selectedContact?.id === id
                  ? updatedContacts.find((c) => c.id === id) || null
                  : get().selectedContact,
              isUpdating: false,
            });

            return true;
          }

          // Get current user
          const {
            data: { user },
          } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('User not authenticated');
          }

          // Verify the contact belongs to the user
          const { data: existingContact } = await supabase
            .from('contacts')
            .select('*')
            .eq('id', id)
            .eq('user_id', user.id)
            .single();

          if (!existingContact) {
            throw new Error('Contact not found or not owned by current user');
          }

          // Update the contact
          const { error } = await supabase
            .from('contacts')
            .update({
              ...updates,
              updated_at: new Date().toISOString(),
            })
            .eq('id', id);

          if (error) {
            throw error;
          }

          // If temperament was updated, recalculate compatibility
          if (
            updates.primary_temperament !== undefined ||
            updates.secondary_temperament !== undefined
          ) {
            // Get user's profile for compatibility calculation
            const { data: userProfile } = await supabase
              .from('personality_profiles')
              .select('dominant_temperament, secondary_temperament')
              .eq('user_id', user.id)
              .single();

            if (userProfile) {
              // Get the updated contact to calculate with
              const { data: updatedContact } = await supabase
                .from('contacts')
                .select('*')
                .eq('id', id)
                .single();

              if (updatedContact && updatedContact.primary_temperament) {
                const compatibilityScore = calculateCompatibility(
                  userProfile.dominant_temperament,
                  userProfile.secondary_temperament,
                  updatedContact.primary_temperament,
                  updatedContact.secondary_temperament
                );

                // Update the compatibility score
                await supabase
                  .from('contacts')
                  .update({ compatibility_score: compatibilityScore })
                  .eq('id', id);

                // Also update locally
                updatedContact.compatibility_score = compatibilityScore;
              }
            }
          }

          // Refresh contacts list
          await get().loadContacts();
          set({ isUpdating: false });
          return true;
        } catch (error: any) {
          console.error('Error updating contact:', error);
          set({
            error: error.message || 'Failed to update contact',
            isUpdating: false,
          });
          return false;
        }
      },

      // Delete a contact
      deleteContact: async (id: string) => {
        try {
          set({ isUpdating: true, error: null });

          // In demo mode, remove from local state only
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            const updatedContacts = get().contacts.filter(
              (contact) => contact.id !== id
            );
            set({
              contacts: updatedContacts,
              filteredContacts: get().applyFiltersAndSort(updatedContacts),
              selectedContact:
                get().selectedContact?.id === id ? null : get().selectedContact,
              isUpdating: false,
            });
            return true;
          }

          // Get current user
          const {
            data: { user },
          } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('User not authenticated');
          }

          // Delete the contact
          const { error } = await supabase
            .from('contacts')
            .delete()
            .eq('id', id)
            .eq('user_id', user.id); // Ensure only deleting own contacts

          if (error) {
            throw error;
          }

          // Update local state
          const updatedContacts = get().contacts.filter(
            (contact) => contact.id !== id
          );
          set({
            contacts: updatedContacts,
            filteredContacts: get().applyFiltersAndSort(updatedContacts),
            selectedContact:
              get().selectedContact?.id === id ? null : get().selectedContact,
            isUpdating: false,
          });

          return true;
        } catch (error: any) {
          console.error('Error deleting contact:', error);
          set({
            error: error.message || 'Failed to delete contact',
            isUpdating: false,
          });
          return false;
        }
      },

      // Set the selected contact
      setSelectedContact: (contact: Contact | null) => {
        set({ selectedContact: contact });
      },

      // Filter contacts by category
      filterContacts: (category: ContactCategory | 'all') => {
        set({
          activeFilter: category,
          filteredContacts: get().applyFiltersAndSort(
            get().contacts,
            category,
            get().activeSort
          ),
        });
      },

      // Sort contacts
      sortContacts: (
        sortBy: 'name' | 'compatibility' | 'category' | 'recent'
      ) => {
        set({
          activeSort: sortBy,
          filteredContacts: get().applyFiltersAndSort(
            get().contacts,
            get().activeFilter,
            sortBy
          ),
        });
      },

      // Search contacts by name
      searchContacts: (query: string) => {
        if (!query) {
          // If query is empty, just apply current filters
          set({
            filteredContacts: get().applyFiltersAndSort(
              get().contacts,
              get().activeFilter,
              get().activeSort
            ),
          });
          return;
        }

        // Filter by name match
        const normalizedQuery = query.toLowerCase();
        const searchResults = get().contacts.filter((contact) =>
          contact.name.toLowerCase().includes(normalizedQuery)
        );

        // Apply current filter and sort to search results
        set({
          filteredContacts: get().applyFiltersAndSort(
            searchResults,
            get().activeFilter,
            get().activeSort
          ),
        });
      },

      // Recalculate compatibility scores for all contacts
      refreshCompatibilityScores: async () => {
        try {
          // In demo mode, update local state only
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            const { primaryTemperament, secondaryTemperament } =
              useProfileStore.getState();
            if (!primaryTemperament) return; // Can't calculate without user temperament

            const updatedContacts = get().contacts.map((contact) => {
              if (contact.primary_temperament) {
                const compatibilityScore = calculateCompatibility(
                  primaryTemperament,
                  secondaryTemperament || null,
                  contact.primary_temperament,
                  contact.secondary_temperament
                );
                return { ...contact, compatibility_score: compatibilityScore };
              }
              return contact;
            });

            set({
              contacts: updatedContacts,
              filteredContacts: get().applyFiltersAndSort(updatedContacts),
            });

            return;
          }

          // Get current user
          const {
            data: { user },
          } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('User not authenticated');
          }

          // Get user's personality profile
          const { data: userProfile } = await supabase
            .from('personality_profiles')
            .select('dominant_temperament, secondary_temperament')
            .eq('user_id', user.id)
            .single();

          if (!userProfile || !userProfile.dominant_temperament) {
            // Can't calculate compatibility without user temperament
            return;
          }

          // Get all contacts
          const { data: contacts } = await supabase
            .from('contacts')
            .select('*')
            .eq('user_id', user.id);

          if (!contacts || contacts.length === 0) {
            return;
          }

          // Update compatibility scores for each contact with temperament data
          for (const contact of contacts) {
            if (contact.primary_temperament) {
              const compatibilityScore = calculateCompatibility(
                userProfile.dominant_temperament,
                userProfile.secondary_temperament,
                contact.primary_temperament,
                contact.secondary_temperament
              );

              // Update in database
              await supabase
                .from('contacts')
                .update({ compatibility_score: compatibilityScore })
                .eq('id', contact.id);

              // Also update locally
              contact.compatibility_score = compatibilityScore;
            }
          }

          // Update state
          set({
            contacts: contacts as Contact[],
            filteredContacts: get().applyFiltersAndSort(contacts as Contact[]),
          });
        } catch (error: any) {
          console.error('Error refreshing compatibility scores:', error);
          // Not setting error state here as this is a background operation
        }
      },

      // Helper function to apply filters and sorting
      applyFiltersAndSort: (
        contacts: Contact[],
        filter: ContactCategory | 'all' = get().activeFilter,
        sort: 'name' | 'compatibility' | 'category' | 'recent' = get()
          .activeSort
      ) => {
        // Apply category filter
        let result =
          filter === 'all'
            ? [...contacts]
            : contacts.filter((contact) => contact.category === filter);

        // Apply sorting
        switch (sort) {
          case 'name':
            result.sort((a, b) => a.name.localeCompare(b.name));
            break;
          case 'compatibility':
            // Sort by compatibility (null values at end)
            result.sort((a, b) => {
              if (
                a.compatibility_score === null &&
                b.compatibility_score === null
              )
                return 0;
              if (a.compatibility_score === null) return 1;
              if (b.compatibility_score === null) return -1;
              return b.compatibility_score - a.compatibility_score;
            });
            break;
          case 'category':
            result.sort((a, b) => a.category.localeCompare(b.category));
            break;
          case 'recent':
            result.sort(
              (a, b) =>
                new Date(b.updated_at).getTime() -
                new Date(a.updated_at).getTime()
            );
            break;
        }

        return result;
      },

      // Clear any error state
      clearError: () => {
        set({ error: null });
      },

      // Load demo contacts
      loadDemoContacts: () => {
        const demoContacts = generateDemoContacts();
        set({
          contacts: demoContacts,
          filteredContacts: demoContacts,
          isDemo: true,
          isLoading: false,
        });
      },
    }),
    {
      name: 'circle-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        contacts: state.isDemo ? state.contacts : [], // Only persist contacts in demo mode
        isDemo: state.isDemo,
      }),
    }
  )
);

// Initialize circle state
export const initializeCircle = async () => {
  const { loadContacts } = useCircleStore.getState();
  await loadContacts();
};
