/*
  # Backfill Missing User Profiles
  
  This migration creates user profiles for any existing auth.users who don't
  have profiles due to the broken trigger.
  
  1. Problem
    - Existing users might not have profiles due to broken trigger
    - These users would experience login loops
  
  2. Solution
    - Find all auth.users without corresponding user_profiles
    - Create profiles for them using the correct schema
*/

-- Create profiles for any existing users who don't have them
INSERT INTO public.user_profiles (
  id,
  full_name,
  email,
  has_completed_assessment,
  created_at,
  updated_at
)
SELECT 
  au.id,
  COALESCE(au.raw_user_meta_data->>'full_name', '') as full_name,
  COALESCE(au.email, '') as email,
  false as has_completed_assessment,
  au.created_at,
  now() as updated_at
FROM auth.users au
LEFT JOIN public.user_profiles up ON au.id = up.id
WHERE up.id IS NULL;

-- Report how many profiles were created
DO $$
DECLARE
  profile_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO profile_count
  FROM public.user_profiles;
  
  RAISE NOTICE 'Total user profiles after backfill: %', profile_count;
END $$;
