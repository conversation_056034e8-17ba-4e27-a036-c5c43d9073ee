/*
  # Add Personality-Specific Quotes and Tips

  1. New Tables
    - `personality_quotes` - Inspirational quotes tailored to each temperament
    - `personality_tips` - Practical tips based on temperament characteristics
  
  2. Content Structure
    - Each quote/tip is linked to a specific temperament
    - Content is categorized by mood and time of day for better personalization
    - Includes priority system for content rotation
  
  3. Security
    - Enable RLS on both tables
    - Add policies for authenticated users to read content
    - Add constraints to ensure data integrity
*/

-- Create personality_quotes table
CREATE TABLE IF NOT EXISTS personality_quotes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  temperament text NOT NULL,
  quote text NOT NULL,
  author text DEFAULT 'Harmona',
  category text DEFAULT 'motivation',
  mood text DEFAULT 'any',
  time_of_day text DEFAULT 'any',
  priority integer DEFAULT 1,
  created_at timestamptz DEFAULT now()
);

-- Create personality_tips table
CREATE TABLE IF NOT EXISTS personality_tips (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  temperament text NOT NULL,
  tip text NOT NULL,
  category text DEFAULT 'general',
  mood text DEFAULT 'any',
  time_of_day text DEFAULT 'any',
  priority integer DEFAULT 1,
  created_at timestamptz DEFAULT now()
);

-- Add constraints for personality_quotes
ALTER TABLE personality_quotes 
ADD CONSTRAINT personality_quotes_temperament_check 
CHECK (temperament = ANY (ARRAY['choleric'::text, 'sanguine'::text, 'melancholic'::text, 'phlegmatic'::text]));

ALTER TABLE personality_quotes 
ADD CONSTRAINT personality_quotes_mood_check 
CHECK (mood = ANY (ARRAY['😄'::text, '🙂'::text, '😐'::text, '😕'::text, '😢'::text, 'any'::text]));

ALTER TABLE personality_quotes 
ADD CONSTRAINT personality_quotes_time_check 
CHECK (time_of_day = ANY (ARRAY['morning'::text, 'afternoon'::text, 'evening'::text, 'any'::text]));

ALTER TABLE personality_quotes 
ADD CONSTRAINT personality_quotes_priority_check 
CHECK (priority >= 1 AND priority <= 10);

-- Add constraints for personality_tips
ALTER TABLE personality_tips 
ADD CONSTRAINT personality_tips_temperament_check 
CHECK (temperament = ANY (ARRAY['choleric'::text, 'sanguine'::text, 'melancholic'::text, 'phlegmatic'::text]));

ALTER TABLE personality_tips 
ADD CONSTRAINT personality_tips_mood_check 
CHECK (mood = ANY (ARRAY['😄'::text, '🙂'::text, '😐'::text, '😕'::text, '😢'::text, 'any'::text]));

ALTER TABLE personality_tips 
ADD CONSTRAINT personality_tips_time_check 
CHECK (time_of_day = ANY (ARRAY['morning'::text, 'afternoon'::text, 'evening'::text, 'any'::text]));

ALTER TABLE personality_tips 
ADD CONSTRAINT personality_tips_priority_check 
CHECK (priority >= 1 AND priority <= 10);

-- Enable RLS
ALTER TABLE personality_quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE personality_tips ENABLE ROW LEVEL SECURITY;

-- Create policies for reading quotes and tips
CREATE POLICY "Authenticated users can read personality quotes"
  ON personality_quotes
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can read personality tips"
  ON personality_tips
  FOR SELECT
  TO authenticated
  USING (true);

-- Insert Choleric Quotes
INSERT INTO personality_quotes (temperament, quote, author, category, mood, time_of_day, priority) VALUES
('choleric', 'Leadership is not about being in charge. It''s about taking care of those in your charge.', 'Simon Sinek', 'leadership', 'any', 'any', 1),
('choleric', 'The way to get started is to quit talking and begin doing.', 'Walt Disney', 'action', '🙂', 'morning', 2),
('choleric', 'Success is not final, failure is not fatal: it is the courage to continue that counts.', 'Winston Churchill', 'resilience', '😕', 'any', 3),
('choleric', 'Don''t watch the clock; do what it does. Keep going.', 'Sam Levenson', 'persistence', '😐', 'afternoon', 4),
('choleric', 'The future belongs to those who believe in the beauty of their dreams.', 'Eleanor Roosevelt', 'ambition', '😄', 'any', 5),
('choleric', 'It is during our darkest moments that we must focus to see the light.', 'Aristotle', 'hope', '😢', 'evening', 6),
('choleric', 'Champions are made from something deep inside them: a desire, a dream, a vision.', 'Muhammad Ali', 'motivation', '😄', 'morning', 7),
('choleric', 'The only impossible journey is the one you never begin.', 'Tony Robbins', 'initiative', '🙂', 'any', 8),
('choleric', 'Strength does not come from physical capacity. It comes from an indomitable will.', 'Mahatma Gandhi', 'willpower', '😕', 'any', 9),
('choleric', 'Excellence is never an accident. It is always the result of high intention and intelligent execution.', 'Aristotle', 'excellence', '😐', 'afternoon', 10);

-- Insert Sanguine Quotes
INSERT INTO personality_quotes (temperament, quote, author, category, mood, time_of_day, priority) VALUES
('sanguine', 'Happiness is not something ready made. It comes from your own actions.', 'Dalai Lama', 'happiness', 'any', 'any', 1),
('sanguine', 'The best way to cheer yourself up is to try to cheer somebody else up.', 'Mark Twain', 'kindness', '🙂', 'any', 2),
('sanguine', 'Life is 10% what happens to you and 90% how you react to it.', 'Charles R. Swindoll', 'optimism', '😕', 'any', 3),
('sanguine', 'Keep your face always toward the sunshine—and shadows will fall behind you.', 'Walt Whitman', 'positivity', '😐', 'morning', 4),
('sanguine', 'The most wasted of days is one without laughter.', 'E.E. Cummings', 'joy', '😄', 'any', 5),
('sanguine', 'In the middle of difficulty lies opportunity.', 'Albert Einstein', 'opportunity', '😢', 'any', 6),
('sanguine', 'A friend is someone who knows all about you and still loves you.', 'Elbert Hubbard', 'friendship', '🙂', 'any', 7),
('sanguine', 'The only way to do great work is to love what you do.', 'Steve Jobs', 'passion', '😄', 'afternoon', 8),
('sanguine', 'Spread love everywhere you go. Let no one ever come to you without leaving happier.', 'Mother Teresa', 'love', '🙂', 'evening', 9),
('sanguine', 'Yesterday is history, tomorrow is a mystery, today is a gift.', 'Eleanor Roosevelt', 'presence', '😐', 'any', 10);

-- Insert Melancholic Quotes
INSERT INTO personality_quotes (temperament, quote, author, category, mood, time_of_day, priority) VALUES
('melancholic', 'The unexamined life is not worth living.', 'Socrates', 'reflection', 'any', 'any', 1),
('melancholic', 'In the depth of winter, I finally learned that there was in me an invincible summer.', 'Albert Camus', 'resilience', '😢', 'any', 2),
('melancholic', 'The beautiful thing about learning is that no one can take it away from you.', 'B.B. King', 'wisdom', '🙂', 'any', 3),
('melancholic', 'Art enables us to find ourselves and lose ourselves at the same time.', 'Thomas Merton', 'creativity', '😄', 'any', 4),
('melancholic', 'The wound is the place where the Light enters you.', 'Rumi', 'healing', '😕', 'evening', 5),
('melancholic', 'What we plant in the soil of contemplation, we shall reap in the harvest of action.', 'Meister Eckhart', 'contemplation', '😐', 'morning', 6),
('melancholic', 'The privilege of a lifetime is to become who you truly are.', 'Carl Jung', 'authenticity', '🙂', 'any', 7),
('melancholic', 'Beauty begins the moment you decide to be yourself.', 'Coco Chanel', 'self-acceptance', '😄', 'any', 8),
('melancholic', 'The quieter you become, the more you are able to hear.', 'Rumi', 'mindfulness', '😐', 'evening', 9),
('melancholic', 'Turn your wounds into wisdom.', 'Oprah Winfrey', 'growth', '😕', 'any', 10);

-- Insert Phlegmatic Quotes
INSERT INTO personality_quotes (temperament, quote, author, category, mood, time_of_day, priority) VALUES
('phlegmatic', 'Peace cannot be kept by force; it can only be achieved by understanding.', 'Albert Einstein', 'peace', 'any', 'any', 1),
('phlegmatic', 'Be yourself; everyone else is already taken.', 'Oscar Wilde', 'authenticity', '🙂', 'any', 2),
('phlegmatic', 'The best and most beautiful things in the world cannot be seen or even touched - they must be felt with the heart.', 'Helen Keller', 'love', '😄', 'any', 3),
('phlegmatic', 'Patience is not the ability to wait, but the ability to keep a good attitude while waiting.', 'Joyce Meyer', 'patience', '😐', 'any', 4),
('phlegmatic', 'In a gentle way, you can shake the world.', 'Mahatma Gandhi', 'gentleness', '🙂', 'any', 5),
('phlegmatic', 'The greatest remedy for anger is delay.', 'Seneca', 'calmness', '😕', 'any', 6),
('phlegmatic', 'Kindness is a language which the deaf can hear and the blind can see.', 'Mark Twain', 'kindness', '😄', 'any', 7),
('phlegmatic', 'Sometimes the most productive thing you can do is relax.', 'Mark Black', 'rest', '😢', 'evening', 8),
('phlegmatic', 'The quieter you become, the more you can hear.', 'Ram Dass', 'listening', '😐', 'evening', 9),
('phlegmatic', 'A gentle answer turns away wrath, but a harsh word stirs up anger.', 'Proverbs 15:1', 'communication', '😕', 'any', 10);

-- Insert Choleric Tips
INSERT INTO personality_tips (temperament, tip, category, mood, time_of_day, priority) VALUES
('choleric', 'Start your day by setting three clear, achievable goals. Your natural drive will help you accomplish them efficiently.', 'productivity', '🙂', 'morning', 1),
('choleric', 'When feeling overwhelmed, take a 5-minute break to reassess priorities. Your strategic mind works best when not rushed.', 'stress-management', '😕', 'any', 2),
('choleric', 'Practice active listening in conversations today. Your leadership shines when others feel heard and valued.', 'communication', '😐', 'any', 3),
('choleric', 'Channel your competitive energy into personal growth rather than comparing yourself to others.', 'self-improvement', '😄', 'any', 4),
('choleric', 'Before making important decisions, pause and consider the impact on your relationships.', 'relationships', '😐', 'afternoon', 5),
('choleric', 'Use your natural confidence to encourage someone who might be struggling today.', 'leadership', '🙂', 'any', 6),
('choleric', 'When facing setbacks, remember that your resilience is one of your greatest strengths.', 'resilience', '😢', 'evening', 7),
('choleric', 'Delegate tasks that others can handle well. This frees you to focus on what truly needs your expertise.', 'delegation', '😐', 'afternoon', 8),
('choleric', 'Take time to celebrate your achievements, no matter how small. Recognition fuels your motivation.', 'self-care', '😄', 'evening', 9),
('choleric', 'Practice patience with processes that can''t be rushed. Some of the best results take time to develop.', 'patience', '😕', 'any', 10);

-- Insert Sanguine Tips
INSERT INTO personality_tips (temperament, tip, category, mood, time_of_day, priority) VALUES
('sanguine', 'Start conversations with genuine compliments. Your natural warmth makes others feel valued and appreciated.', 'social', '🙂', 'any', 1),
('sanguine', 'When feeling down, reach out to a friend. Your social connections are a powerful source of energy and healing.', 'emotional-support', '😕', 'any', 2),
('sanguine', 'Use your enthusiasm to motivate others, but remember to listen to their concerns too.', 'communication', '😄', 'any', 3),
('sanguine', 'Schedule regular social activities to maintain your energy levels and positive outlook.', 'self-care', '😐', 'any', 4),
('sanguine', 'Turn routine tasks into games or challenges to make them more engaging and fun.', 'productivity', '😐', 'afternoon', 5),
('sanguine', 'Share your optimism with someone who needs encouragement today. Your positivity is contagious.', 'kindness', '🙂', 'any', 6),
('sanguine', 'When overwhelmed, break large tasks into smaller, more manageable pieces that feel less daunting.', 'organization', '😢', 'any', 7),
('sanguine', 'Use your natural storytelling ability to make presentations and conversations more engaging.', 'communication', '😄', 'afternoon', 8),
('sanguine', 'Practice mindfulness to stay present instead of getting too caught up in future possibilities.', 'mindfulness', '😐', 'evening', 9),
('sanguine', 'Remember that it''s okay to have quiet moments. Not every interaction needs to be high-energy.', 'balance', '😕', 'evening', 10);

-- Insert Melancholic Tips
INSERT INTO personality_tips (temperament, tip, category, mood, time_of_day, priority) VALUES
('melancholic', 'Set aside 15 minutes for reflection or journaling. Your introspective nature thrives with dedicated thinking time.', 'self-reflection', '😐', 'evening', 1),
('melancholic', 'When feeling overwhelmed by details, step back and focus on the bigger picture and purpose.', 'perspective', '😕', 'any', 2),
('melancholic', 'Use your analytical skills to break down complex problems into manageable steps.', 'problem-solving', '🙂', 'any', 3),
('melancholic', 'Share your creative ideas with others. Your unique perspective adds valuable depth to discussions.', 'creativity', '😄', 'any', 4),
('melancholic', 'Practice self-compassion when things don''t meet your high standards. Progress is more important than perfection.', 'self-acceptance', '😕', 'any', 5),
('melancholic', 'Schedule regular alone time to recharge your emotional and mental energy.', 'self-care', '😐', 'any', 6),
('melancholic', 'When feeling sad, remember that your depth of emotion is also a source of great empathy and connection.', 'emotional-intelligence', '😢', 'evening', 7),
('melancholic', 'Use your attention to detail to create something beautiful or meaningful today.', 'creativity', '🙂', 'afternoon', 8),
('melancholic', 'Trust your intuition when making decisions. Your thoughtful nature usually leads to wise choices.', 'decision-making', '😐', 'any', 9),
('melancholic', 'Connect with others who appreciate depth and meaningful conversation. Quality over quantity in relationships.', 'relationships', '😄', 'any', 10);

-- Insert Phlegmatic Tips
INSERT INTO personality_tips (temperament, tip, category, mood, time_of_day, priority) VALUES
('phlegmatic', 'Use your natural calming presence to help someone who seems stressed or anxious today.', 'support', '🙂', 'any', 1),
('phlegmatic', 'When making decisions, trust your instinct for what feels right and harmonious.', 'decision-making', '😐', 'any', 2),
('phlegmatic', 'Set gentle, achievable goals that align with your values rather than external pressures.', 'goal-setting', '🙂', 'morning', 3),
('phlegmatic', 'Practice saying no to commitments that drain your energy or conflict with your peace.', 'boundaries', '😕', 'any', 4),
('phlegmatic', 'Use your diplomatic skills to mediate conflicts or bring people together.', 'peacemaking', '😐', 'afternoon', 5),
('phlegmatic', 'Take breaks throughout the day to maintain your energy and avoid burnout.', 'self-care', '😐', 'any', 6),
('phlegmatic', 'When feeling overwhelmed, return to activities that bring you peace and restore your balance.', 'stress-relief', '😢', 'evening', 7),
('phlegmatic', 'Share your perspective in group discussions. Your balanced viewpoint often provides valuable insight.', 'communication', '😄', 'any', 8),
('phlegmatic', 'Create a peaceful environment in your space with elements that bring you comfort and calm.', 'environment', '🙂', 'any', 9),
('phlegmatic', 'Remember that your steady, reliable nature is a gift that others deeply appreciate and depend on.', 'self-appreciation', '😕', 'evening', 10);

-- Create functions to get random quotes and tips based on temperament and mood
CREATE OR REPLACE FUNCTION get_personality_quote(
  user_temperament text,
  user_mood text DEFAULT 'any',
  time_context text DEFAULT 'any'
)
RETURNS TABLE(quote text, author text, category text)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- First try to get quotes that match mood and time
  RETURN QUERY
  SELECT pq.quote, pq.author, pq.category
  FROM personality_quotes pq
  WHERE pq.temperament = user_temperament
    AND (pq.mood = user_mood OR pq.mood = 'any')
    AND (pq.time_of_day = time_context OR pq.time_of_day = 'any')
  ORDER BY RANDOM()
  LIMIT 1;
  
  -- If no results, get any quote for the temperament
  IF NOT FOUND THEN
    RETURN QUERY
    SELECT pq.quote, pq.author, pq.category
    FROM personality_quotes pq
    WHERE pq.temperament = user_temperament
    ORDER BY RANDOM()
    LIMIT 1;
  END IF;
END;
$$;

CREATE OR REPLACE FUNCTION get_personality_tip(
  user_temperament text,
  user_mood text DEFAULT 'any',
  time_context text DEFAULT 'any'
)
RETURNS TABLE(tip text, category text)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- First try to get tips that match mood and time
  RETURN QUERY
  SELECT pt.tip, pt.category
  FROM personality_tips pt
  WHERE pt.temperament = user_temperament
    AND (pt.mood = user_mood OR pt.mood = 'any')
    AND (pt.time_of_day = time_context OR pt.time_of_day = 'any')
  ORDER BY RANDOM()
  LIMIT 1;
  
  -- If no results, get any tip for the temperament
  IF NOT FOUND THEN
    RETURN QUERY
    SELECT pt.tip, pt.category
    FROM personality_tips pt
    WHERE pt.temperament = user_temperament
    ORDER BY RANDOM()
    LIMIT 1;
  END IF;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_personality_quote(text, text, text) TO authenticated;
GRANT EXECUTE ON FUNCTION get_personality_tip(text, text, text) TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS personality_quotes_temperament_idx ON personality_quotes(temperament);
CREATE INDEX IF NOT EXISTS personality_quotes_mood_idx ON personality_quotes(mood);
CREATE INDEX IF NOT EXISTS personality_quotes_time_idx ON personality_quotes(time_of_day);

CREATE INDEX IF NOT EXISTS personality_tips_temperament_idx ON personality_tips(temperament);
CREATE INDEX IF NOT EXISTS personality_tips_mood_idx ON personality_tips(mood);
CREATE INDEX IF NOT EXISTS personality_tips_time_idx ON personality_tips(time_of_day);