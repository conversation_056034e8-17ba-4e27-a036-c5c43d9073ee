-- Add email and phone columns to circle_contacts table if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'circle_contacts' AND column_name = 'email'
  ) THEN
    ALTER TABLE circle_contacts ADD COLUMN email TEXT;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'circle_contacts' AND column_name = 'phone'
  ) THEN
    ALTER TABLE circle_contacts ADD COLUMN phone TEXT;
  END IF;
END $$;

-- Update the RLS policy to ensure users can only access their own contacts
DROP POLICY IF EXISTS "Users can manage own circle contacts" ON circle_contacts;

CREATE POLICY "Users can manage own circle contacts"
  ON circle_contacts
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);