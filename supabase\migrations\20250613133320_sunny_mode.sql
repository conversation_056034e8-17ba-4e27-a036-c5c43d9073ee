/*
  # Initial Schema Setup for Harmona Personality App

  1. New Tables
    - `user_profiles` - Extended user information
    - `personality_profiles` - Personality assessment results
    - `mood_entries` - Daily mood tracking
    - `daily_insights` - Personalized daily content
    - `circle_contacts` - User's relationship circle
    - `assessment_responses` - Individual assessment answers

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users to access their own data
*/

-- User profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name text NOT NULL DEFAULT '',
  email text NOT NULL DEFAULT '',
  has_completed_assessment boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Personality profiles table
CREATE TABLE IF NOT EXISTS personality_profiles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  dominant_temperament text NOT NULL CHECK (dominant_temperament IN ('choleric', 'sanguine', 'melancholic', 'phlegmatic')),
  secondary_temperament text NOT NULL CHECK (secondary_temperament IN ('choleric', 'sanguine', 'melancholic', 'phlegmatic')),
  dominant_percentage integer NOT NULL CHECK (dominant_percentage >= 0 AND dominant_percentage <= 100),
  secondary_percentage integer NOT NULL CHECK (secondary_percentage >= 0 AND secondary_percentage <= 100),
  assessment_completed_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now()
);

-- Mood entries table
CREATE TABLE IF NOT EXISTS mood_entries (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  emoji text NOT NULL,
  description text DEFAULT '',
  created_at timestamptz DEFAULT now()
);

-- Daily insights table
CREATE TABLE IF NOT EXISTS daily_insights (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  temperament text NOT NULL CHECK (temperament IN ('choleric', 'sanguine', 'melancholic', 'phlegmatic')),
  insight text NOT NULL,
  tip text NOT NULL,
  quote text NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Circle contacts table
CREATE TABLE IF NOT EXISTS circle_contacts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name text NOT NULL,
  category text NOT NULL CHECK (category IN ('family', 'work', 'friends', 'other')),
  dominant_temperament text CHECK (dominant_temperament IN ('choleric', 'sanguine', 'melancholic', 'phlegmatic')),
  secondary_temperament text CHECK (secondary_temperament IN ('choleric', 'sanguine', 'melancholic', 'phlegmatic')),
  created_at timestamptz DEFAULT now()
);

-- Assessment responses table
CREATE TABLE IF NOT EXISTS assessment_responses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  question_number integer NOT NULL,
  selected_answer integer NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE personality_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE mood_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE circle_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_responses ENABLE ROW LEVEL SECURITY;

-- Policies for user_profiles
CREATE POLICY "Users can read own profile"
  ON user_profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile"
  ON user_profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile"
  ON user_profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- Policies for personality_profiles
CREATE POLICY "Users can read own personality profiles"
  ON personality_profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own personality profiles"
  ON personality_profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Policies for mood_entries
CREATE POLICY "Users can read own mood entries"
  ON mood_entries
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own mood entries"
  ON mood_entries
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Policies for daily_insights (read-only for all authenticated users)
CREATE POLICY "Authenticated users can read daily insights"
  ON daily_insights
  FOR SELECT
  TO authenticated
  USING (true);

-- Policies for circle_contacts
CREATE POLICY "Users can manage own circle contacts"
  ON circle_contacts
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Policies for assessment_responses
CREATE POLICY "Users can read own assessment responses"
  ON assessment_responses
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own assessment responses"
  ON assessment_responses
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Insert sample daily insights
INSERT INTO daily_insights (temperament, insight, tip, quote) VALUES
('choleric', 'Your natural leadership abilities shine brightest when you balance decisiveness with empathy.', 'Take a moment to listen to others'' perspectives before making decisions today.', '"The best leaders are those who can inspire others to achieve greatness." - Unknown'),
('sanguine', 'Your enthusiasm is contagious and brings joy to those around you.', 'Channel your energy into one meaningful project today rather than spreading yourself thin.', '"Enthusiasm is the electricity of life." - Gordon Parks'),
('melancholic', 'Your thoughtful nature allows you to see beauty and meaning in everyday moments.', 'Set aside time for reflection today, but don''t get lost in overthinking.', '"The unexamined life is not worth living." - Socrates'),
('phlegmatic', 'Your calm presence provides stability and comfort to others during turbulent times.', 'Trust your instincts today and don''t be afraid to share your valuable insights.', '"In the midst of winter, I found there was, within me, an invincible summer." - Albert Camus');

-- Function to automatically create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.user_profiles (id, full_name, email)
  VALUES (
    new.id,
    COALESCE(new.raw_user_meta_data->>'full_name', ''),
    COALESCE(new.email, '')
  );
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create user profile
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();