# Harmona System Patterns

## System Architecture

Harmona follows a modern Expo React Native architecture with Supabase as the backend service. The system is designed with the following high-level architecture:

```mermaid
graph TD
    A[Expo App] --> B[Expo Router]
    B --> C[UI Components]
    B --> D[Hooks & Logic]
    A --> E[Supabase Client]
    E --> F[Authentication]
    E --> G[Database]
    E --> H[Storage]
    A --> I[OpenRouter Client]
    I --> J[AI Services]
```

### Key Architectural Components

1. **Frontend UI Layer** (React Native)

   - Expo Router for navigation
   - React components for UI rendering
   - Custom hooks for business logic

2. **Data Management Layer**

   - Supabase client for data operations
   - Local state management
   - Data transformation and processing

3. **Backend Services** (Supabase)

   - Authentication services
   - PostgreSQL database
   - Storage for media files
   - Supabase Edge Functions

4. **External Integrations**
   - OpenRouter for AI-generated content
   - Notification services (future)

## Navigation Architecture

Harmona uses Expo Router for file-based navigation with a structured route grouping approach:

```
app/
├── _layout.tsx            # Root layout with stack navigation
├── index.tsx              # Splash/Loading screen
├── +not-found.tsx         # 404 error screen
├── (auth)/                # Authentication route group
│   ├── _layout.tsx        # Auth layout
│   ├── index.tsx          # Auth landing
│   ├── login.tsx          # Login screen
│   ├── register.tsx       # Registration screen
│   ├── forgot-password.tsx # Password recovery
│   └── verify-email.tsx   # Email verification
├── assessment/            # Personality assessment route group
│   ├── _layout.tsx        # Assessment layout
│   └── index.tsx          # Assessment screen
├── result/                # Assessment results route group
│   ├── _layout.tsx        # Results layout
│   └── index.tsx          # Results screen
└── (tabs)/                # Main app tabs route group
    ├── _layout.tsx        # Tab navigation layout
    ├── index.tsx          # Home tab
    ├── compare.tsx        # Compare tab
    ├── talk.tsx           # AI Talk tab
    ├── circle.tsx         # Relationship circle tab
    └── settings.tsx       # Settings tab
```

### Navigation Flow Patterns

1. **Authentication Flow**:

   - App starts → Splash screen
   - Check authentication status
   - If not authenticated → Auth screens
   - If authenticated but email not verified → Verification screen
   - If authenticated but assessment not completed → Assessment screens
   - If fully authenticated → Main tabs

2. **Assessment Flow**:

   - Assessment introduction → Questions
   - Questions → Processing
   - Processing → Results
   - Results → Main tabs

3. **Tab Navigation Flow**:
   - Bottom tab bar for main sections
   - Stack navigation within each tab for drill-down
   - Modal screens for quick actions

## Database Schema

The database follows a relational model with the following core tables:

```mermaid
erDiagram
    auth_users ||--o{ user_profiles : has
    user_profiles ||--o{ personality_profiles : has
    user_profiles ||--o{ mood_entries : records
    user_profiles ||--o{ circle_contacts : manages
    user_profiles ||--o{ assessment_responses : submits
    daily_insights }o--o{ personality_profiles : matches
```

### Key Tables

1. **user_profiles**

   - Extends auth.users with application-specific user data
   - Tracks completion status of assessment
   - Stores basic profile information

2. **personality_profiles**

   - Stores results of personality assessment
   - Records dominant and secondary temperaments
   - Contains percentages for each temperament

3. **mood_entries**

   - Tracks user mood logs over time
   - Links mood to user profile
   - Stores timestamp for trend analysis

4. **daily_insights**

   - Contains templated content for each temperament
   - Rotates through different insights
   - Categorized by insight type (tip, quote, general)

5. **circle_contacts**

   - Stores user's important relationships
   - Records temperament information for contacts
   - Categorizes relationships

6. **assessment_responses**
   - Records individual question responses
   - Allows for reassessment and comparison
   - Enables detailed temperament analysis

## Authentication Pattern

Harmona implements a comprehensive authentication flow using Supabase Auth:

```mermaid
sequenceDiagram
    Actor User
    participant App
    participant Supabase
    participant Email

    User->>App: Open app
    App->>Supabase: Check session

    alt No session
        App->>User: Show auth screen
        User->>App: Enter credentials
        App->>Supabase: Sign in request
        Supabase->>App: Auth response
    else Session exists
        Supabase->>App: Return session
    end

    App->>Supabase: Get user profile

    alt Email not verified
        App->>User: Show verification screen
        User->>App: Request verification email
        App->>Supabase: Trigger function
        Supabase->>Email: Send verification email
        User->>Email: Open verification link
    end

    alt Assessment not completed
        App->>User: Show assessment screen
    else Assessment completed
        App->>User: Show main app
    end
```

### Auth Security Patterns

1. **Row Level Security (RLS)**

   - All tables have RLS enabled
   - Policies restrict users to their own data
   - Public routes have limited access

2. **JWT Handling**

   - Tokens managed by Supabase client
   - Auto-refresh enabled
   - Session persistence implemented

3. **Demo Mode**
   - Fallback to mock client when Supabase not configured
   - Simulated data for demonstration
   - Clear indicators of demo status

## Data Flow Patterns

Harmona follows these data flow patterns:

1. **Read Operations**:

   ```
   UI Component → Custom Hook → Supabase Client → Database → Component State
   ```

2. **Write Operations**:

   ```
   User Action → UI Component → Custom Hook → Validation → Supabase Client → Database → Update UI
   ```

3. **Real-time Updates** (future):

   ```
   Database Change → Supabase Subscription → State Update → UI Refresh
   ```

4. **AI Content Generation**:
   ```
   User Input → OpenRouter Client → AI Model → Response Processing → UI Display
   ```

## Component Patterns

Harmona's UI components follow these patterns:

### Composition Pattern

```jsx
// Example of component composition
<Card>
  <Card.Header>
    <Typography variant="heading">Daily Insight</Typography>
  </Card.Header>
  <Card.Content>
    <InsightContent temperament={user.dominantTemperament} />
  </Card.Content>
  <Card.Footer>
    <ActionButton />
  </Card.Footer>
</Card>
```

### Container/Presentation Pattern

- **Container Components**: Handle data fetching, processing, and state
- **Presentation Components**: Focus on rendering and UI interactions

```jsx
// Container component example
const HomeScreenContainer = () => {
  const { profile, insights, loading } = useHomeData();

  if (loading) return <LoadingSpinner />;

  return <HomeScreen profile={profile} insights={insights} />;
};

// Presentation component example
const HomeScreen = ({ profile, insights }) => (
  <ScrollView>
    <GreetingSection name={profile.fullName} />
    <InsightCard insight={insights.daily} />
    <MoodTracker currentMood={profile.lastMood} />
  </ScrollView>
);
```

### Custom Hooks Pattern

```javascript
// Example custom hook for personality data
const usePersonalityProfile = (userId) => {
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('personality_profiles')
          .select('*')
          .eq('user_id', userId)
          .single();

        if (error) throw error;
        setProfile(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [userId]);

  return { profile, loading, error };
};
```

## Error Handling Pattern

Harmona implements a multi-layered error handling approach:

1. **API Error Handling**:

   - Try/catch blocks for all async operations
   - Specific error handling for Supabase errors
   - Fallback content for API failures

2. **UI Error States**:

   - Loading indicators during async operations
   - Error components for failed operations
   - Retry mechanisms for transient errors

3. **Global Error Boundary**:
   - React error boundaries for component errors
   - Fallback UI for catastrophic failures
   - Error reporting (future implementation)

## Performance Optimization Patterns

1. **Image Optimization**:

   - Lazy loading for images
   - Proper sizing and resolution
   - Caching strategies

2. **Rendering Optimization**:

   - Memoization for expensive computations
   - Component splitting for targeted re-renders
   - List virtualization for long scrolling content

3. **Network Optimization**:
   - Request batching where appropriate
   - Caching of repeated requests
   - Offline capabilities (future implementation)

## Security Patterns

1. **Data Protection**:

   - Environment variables for sensitive configuration
   - No client-side storage of sensitive data
   - Proper JWT handling

2. **Input Validation**:

   - Client-side validation for user experience
   - Server-side validation via Supabase RLS
   - Type checking with TypeScript

3. **Authentication Security**:
   - Email verification requirement
   - Password strength requirements
   - Session management and timeout

## Testing Patterns

1. **Unit Testing**:

   - Jest for testing utilities and helpers
   - Isolated component testing

2. **Integration Testing**:

   - Testing component interactions
   - API integration tests

3. **End-to-End Testing**:
   - Full user flow testing (future)
   - Critical path validation

## Deployment Pattern

Harmona follows the Expo EAS build system for deployment:

1. **Development Builds**:

   - Local testing with Expo Go
   - Development client for native module testing

2. **Preview Builds**:

   - Generated for testing specific features
   - Distributed to internal testers

3. **Production Builds**:
   - Full optimized builds for app stores
   - OTA updates for non-native changes

## Critical Implementation Paths

1. **Authentication Flow**

   - Splash screen → Auth check → Appropriate routing
   - Email verification handling
   - Profile creation on signup

2. **Assessment System**

   - Question presentation → Answer collection → Score calculation
   - Temperament determination algorithm
   - Results storage and presentation

3. **Insights Generation**

   - Temperament-based content selection
   - Personalization of daily content
   - AI-enhanced content creation

4. **Relationship Management**
   - Contact data storage and retrieval
   - Compatibility calculations
   - Communication recommendations

These system patterns form the foundation of Harmona's architecture and should guide all implementation decisions to ensure consistency, maintainability, and scalability.
