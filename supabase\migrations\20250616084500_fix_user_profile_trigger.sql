/*
  # Fix User Profile Creation Trigger
  
  This migration fixes the conflicting user profile creation triggers that were
  trying to insert into a non-existent 'email_verified' field, causing user
  registration and login to fail.
  
  1. Problem
    - Multiple conflicting handle_new_user() functions
    - Some trying to insert into non-existent 'email_verified' field
    - User profile creation failing during registration
    - Login looping because profiles never get created
  
  2. Solution
    - Drop all existing triggers and functions
    - Create single, correct trigger that matches actual table schema
    - Only insert fields that actually exist in user_profiles table
  
  3. Fields in user_profiles table (from schema):
    - id (uuid, references auth.users)
    - full_name (text)
    - email (text)
    - has_completed_assessment (boolean)
    - created_at (timestamptz)
    - updated_at (timestamptz)
*/

-- Drop all existing triggers and functions to start clean
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Create the correct function that matches the actual table schema
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  -- Insert user profile with only fields that actually exist in the table
  INSERT INTO public.user_profiles (
    id,
    full_name,
    email,
    has_completed_assessment,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.email, ''),
    false,
    now(),
    now()
  );
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the user creation
    RAISE WARNING 'Failed to create user profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger to automatically create user profiles
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Verify the trigger is working by checking if it exists
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.triggers 
    WHERE trigger_name = 'on_auth_user_created' 
    AND event_object_table = 'users'
  ) THEN
    RAISE NOTICE 'User profile creation trigger successfully created';
  ELSE
    RAISE EXCEPTION 'Failed to create user profile trigger';
  END IF;
END $$;
