/*
  # Create subgreetings table for dynamic personality-based messages

  1. New Tables
    - `subgreetings`
      - `id` (uuid, primary key)
      - `temperament` (text, references personality types)
      - `message` (text, the greeting message)
      - `mood` (text, optional mood context)
      - `time_of_day` (text, morning/afternoon/evening)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on `subgreetings` table
    - Add policy for authenticated users to read subgreetings
    - Add constraint to ensure valid temperament values

  3. Sample Data
    - Insert diverse greeting messages for each temperament
    - Include time-based and mood-based variations
*/

-- Create subgreetings table
CREATE TABLE IF NOT EXISTS subgreetings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  temperament text NOT NULL,
  message text NOT NULL,
  mood text DEFAULT 'neutral',
  time_of_day text DEFAULT 'any',
  created_at timestamptz DEFAULT now()
);

-- Add constraints
ALTER TABLE subgreetings 
ADD CONSTRAINT subgreetings_temperament_check 
CHECK (temperament = ANY (ARRAY['choleric'::text, 'sanguine'::text, 'melancholic'::text, 'phlegmatic'::text]));

ALTER TABLE subgreetings 
ADD CONSTRAINT subgreetings_mood_check 
CHECK (mood = ANY (ARRAY['energetic'::text, 'calm'::text, 'focused'::text, 'creative'::text, 'social'::text, 'reflective'::text, 'neutral'::text]));

ALTER TABLE subgreetings 
ADD CONSTRAINT subgreetings_time_check 
CHECK (time_of_day = ANY (ARRAY['morning'::text, 'afternoon'::text, 'evening'::text, 'any'::text]));

-- Enable RLS
ALTER TABLE subgreetings ENABLE ROW LEVEL SECURITY;

-- Create policy for reading subgreetings
CREATE POLICY "Authenticated users can read subgreetings"
  ON subgreetings
  FOR SELECT
  TO authenticated
  USING (true);

-- Insert sample subgreetings for Choleric temperament
INSERT INTO subgreetings (temperament, message, mood, time_of_day) VALUES
('choleric', 'ready to conquer your goals today', 'energetic', 'morning'),
('choleric', 'in full leadership mode', 'focused', 'any'),
('choleric', 'driven and unstoppable', 'energetic', 'any'),
('choleric', 'focused on achieving excellence', 'focused', 'afternoon'),
('choleric', 'channeling your natural determination', 'energetic', 'any'),
('choleric', 'ready to take charge and make things happen', 'energetic', 'morning'),
('choleric', 'in your element as a natural leader', 'focused', 'any'),
('choleric', 'powered by ambition and drive', 'energetic', 'any'),
('choleric', 'ready to tackle challenges head-on', 'focused', 'afternoon'),
('choleric', 'embracing your decisive nature', 'focused', 'evening');

-- Insert sample subgreetings for Sanguine temperament
INSERT INTO subgreetings (temperament, message, mood, time_of_day) VALUES
('sanguine', 'radiating positive energy and enthusiasm', 'energetic', 'morning'),
('sanguine', 'bringing joy to everyone around you', 'social', 'any'),
('sanguine', 'spreading optimism wherever you go', 'social', 'any'),
('sanguine', 'lighting up the room with your presence', 'social', 'afternoon'),
('sanguine', 'embracing life with infectious enthusiasm', 'energetic', 'any'),
('sanguine', 'connecting hearts and building friendships', 'social', 'any'),
('sanguine', 'turning ordinary moments into adventures', 'energetic', 'morning'),
('sanguine', 'inspiring others with your zest for life', 'social', 'any'),
('sanguine', 'creating smiles and spreading happiness', 'social', 'afternoon'),
('sanguine', 'celebrating the beauty in everyday moments', 'social', 'evening');

-- Insert sample subgreetings for Melancholic temperament
INSERT INTO subgreetings (temperament, message, mood, time_of_day) VALUES
('melancholic', 'diving deep into thoughtful reflection', 'reflective', 'morning'),
('melancholic', 'appreciating the beauty in details', 'creative', 'any'),
('melancholic', 'channeling your analytical brilliance', 'focused', 'any'),
('melancholic', 'creating something meaningful and lasting', 'creative', 'afternoon'),
('melancholic', 'finding profound insights in quiet moments', 'reflective', 'any'),
('melancholic', 'expressing your rich inner world', 'creative', 'any'),
('melancholic', 'seeking perfection in your endeavors', 'focused', 'morning'),
('melancholic', 'connecting with deeper truths and meanings', 'reflective', 'evening'),
('melancholic', 'transforming thoughts into beautiful creations', 'creative', 'afternoon'),
('melancholic', 'embracing the complexity of emotions', 'reflective', 'any');

-- Insert sample subgreetings for Phlegmatic temperament
INSERT INTO subgreetings (temperament, message, mood, time_of_day) VALUES
('phlegmatic', 'bringing peace and harmony to your surroundings', 'calm', 'morning'),
('phlegmatic', 'being the steady anchor others rely on', 'calm', 'any'),
('phlegmatic', 'creating a sense of comfort and stability', 'calm', 'any'),
('phlegmatic', 'nurturing relationships with gentle care', 'social', 'afternoon'),
('phlegmatic', 'finding strength in your patient nature', 'calm', 'any'),
('phlegmatic', 'building bridges and healing connections', 'social', 'any'),
('phlegmatic', 'embracing the power of quiet wisdom', 'reflective', 'morning'),
('phlegmatic', 'offering support with your loyal heart', 'social', 'any'),
('phlegmatic', 'maintaining balance in a chaotic world', 'calm', 'afternoon'),
('phlegmatic', 'spreading tranquility through your presence', 'calm', 'evening');