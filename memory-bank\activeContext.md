# Harmona Active Context

## Current Focus

As of June 15, 2025, the current development focus for Harmona is:

1. **State Management Implementation**: Implementing a comprehensive state management solution using Zustand to replace direct component state, with a focus on creating specialized stores for different domains of the application.

2. **Core Infrastructure Enhancement**: Building upon the foundational elements like authentication and navigation by adding proper state management and data persistence.

3. **Data Flow Optimization**: Ensuring proper data flow between the Supabase backend and the UI components through well-structured state stores.

## Recent Changes

Based on the project's codebase examination and development progress, the following recent changes have been made:

### State Management Implementation

- Added Zustand dependency for state management
- Created `stores` directory at the root level
- Implemented Authentication Store (`authStore.ts`) with full auth functionality
- Implemented Profile Store (`profileStore.ts`) for user profile and personality data
- Implemented Assessment Store (`assessmentStore.ts`) with advanced three-phase assessment flow:
  - Initial Phase (Q1-4): Presents all four temperaments to establish baseline scores
  - Confirmation Phase (Q5-6): Focuses on the two highest-scoring temperaments
  - Comparison Phase (Q7-10): Only presents compatible temperament combinations
- Implemented Mood Store (`moodStore.ts`) with comprehensive mood tracking capabilities:
  - Current mood tracking
  - Historical mood data management
  - Mood analytics with time-based insights
  - Support for mood tagging and descriptions
  - Time-of-day pattern recognition
- Set up proper AsyncStorage persistence for offline data access
- Implemented demo mode support in all stores

### Database Schema Evolution

- Initial schema setup with core tables (user_profiles, personality_profiles, mood_entries, etc.)
- Implementation of Row Level Security (RLS) policies
- Creation of database triggers for automatic profile creation
- Multiple incremental migrations to expand functionality

### Authentication System

- Implementation of email-based authentication
- Email verification flow
- Fallback demo mode for testing without Supabase configuration

### Navigation Structure

- Implementation of file-based routing with Expo Router
- Tab-based main navigation with home, compare, talk, circle, and settings screens
- Authentication and assessment flow screens

## Active Decisions and Considerations

### Critical Issues to Address

1. **Memory Bank Documentation**

   - The Memory Bank structure has been established but needs to be kept updated with the latest progress
   - Regular updates to activeContext.md and progress.md to reflect development status

2. **Folder Structure Standardization**

   - Current structure doesn't fully align with Expo best practices
   - Need to reorganize component structure to match the recommended pattern:

   ```
   src/
   ├── components/     # Reusable components
   ├── hooks/          # Custom hooks
   ├── lib/            # Utilities & config
   ├── stores/         # State management (implemented)
   └── types/          # TypeScript definitions
   ```

3. **Missing Dependencies**

   - Form management: Consider adding React Hook Form for better form handling
   - Image optimization: Add expo-image for better performance
   - Testing framework: Add Jest and React Testing Library

4. **Type Safety Improvements**
   - Database schema and TypeScript type definitions are not consistently aligned
   - Some interfaces define fields not present in the database (e.g., email_verified in UserProfile)
   - Need to implement automated type generation from Supabase schema

### Architecture Decisions

1. **State Management Approach**

   - Decision to implement Zustand for state management to replace direct React state usage
   - Implementation of separate stores for authentication, user profile, assessment, and mood tracking
   - Plan to create additional stores for circle (relationships) and settings
   - Established pattern of implementing demo mode, persistence, and error handling consistently across stores

2. **Component Strategy**

   - Create a library of reusable UI components to ensure consistency
   - Implement container/presentation pattern for better separation of concerns
   - Add proper component documentation and typing

3. **Authentication Enhancement**

   - Improve email verification flow with more robust error handling
   - Add password strength validation
   - Enhance demo mode with more realistic data

4. **Performance Optimization**
   - Implement memoization for expensive calculations
   - Add proper loading states and skeleton screens
   - Optimize image handling and network requests

## Implementation Insights

### Current Patterns

1. **Auth Flow Implementation**

   ```
   Login/Register → Email Verification → Assessment → Main App
   ```

2. **Navigation Structure**

   ```
   Root (Stack)
   ├── Splash/Auth Check
   ├── Auth Group
   │   ├── Login
   │   ├── Register
   │   └── Verification
   ├── Assessment
   ├── Results
   └── Tabs Group
       ├── Home
       ├── Compare
       ├── Talk
       ├── Circle
       └── Settings
   ```

3. **Data Fetching Pattern**
   ```javascript
   const fetchData = async () => {
     try {
       setLoading(true);
       const { data, error } = await supabase.from('table').select('*');
       if (error) throw error;
       setData(data);
     } catch (error) {
       setError(error.message);
     } finally {
       setLoading(false);
     }
   };
   ```

### Identified Anti-Patterns

1. **Direct Component State**: Many components manage their own state rather than using a central state management solution.

2. **Prop Drilling**: Some data is passed through multiple component layers rather than using context or state management.

3. **Inline Styles**: Some components use inline styles rather than a consistent styling system.

4. **Missing Loading States**: Not all async operations properly handle loading states.

5. **Environment Variables**: API keys directly committed to the repo rather than using a secure approach.

## Next Steps

Based on the project assessment, the following next steps are prioritized:

1. **Complete Memory Bank Documentation**

   - Create remaining core files
   - Ensure comprehensive documentation of all aspects of the project

2. **Implement Remaining State Stores**

   - Implement Circle Store for relationship management
   - Implement Settings Store for app configuration
   - Connect stores to relevant UI components

3. **Reorganize Folder Structure**

   - Create proper src/ structure
   - Move components to appropriate directories
   - Implement consistent file naming and organization

4. **Enhance Component Library**

   - Create reusable UI components
   - Implement proper component documentation
   - Add storybook or similar for component visualization

5. **Fix Type Safety Issues**

   - Align TypeScript interfaces with database schema
   - Add proper validation for form inputs
   - Implement automated type generation

6. **Add Testing Infrastructure**

   - Set up Jest and React Testing Library
   - Create unit tests for utilities and components
   - Implement E2E testing strategy

7. **Optimize Performance**
   - Implement memoization for expensive calculations
   - Add proper loading states and skeleton screens
   - Optimize image handling and network requests

## Development Preferences

Based on the code review, the following preferences and patterns should be maintained:

1. **TypeScript Conventions**

   - Use TypeScript interfaces for all data structures
   - Prefer explicit typing over inference where clarity is needed
   - Use type guards for runtime type checking

2. **Component Structure**

   - Functional components with hooks
   - Clear separation of UI and logic
   - Consistent prop naming and ordering

3. **Styling Approach**

   - Use StyleSheet.create for style definitions
   - Consistent color and spacing variables
   - Component-specific style objects

4. **Error Handling**

   - Try/catch blocks for all async operations
   - User-friendly error messages
   - Fallback UI for error states

5. **Code Formatting**
   - Consistent indentation (2 spaces)
   - Clear function and variable naming
   - JSDoc comments for complex functions

## Important Learnings

Through the project review, several important insights have been gained:

1. **Authentication Complexity**: The email verification flow requires careful handling of edge cases and error states.

2. **Navigation Structure**: Expo Router provides a powerful but opinionated navigation system that requires careful structure planning.

3. **Supabase Integration**: The Supabase client setup needs fallback handling for cases where environment variables are not properly configured.

4. **TypeScript/Database Alignment**: Keeping TypeScript interfaces in sync with database schema requires careful management or automation.

5. **Demo Mode Importance**: A well-implemented demo mode significantly improves the development and testing experience.

6. **Multi-Phase Assessment Logic**: Breaking down the personality assessment into distinct phases (initial, confirmation, comparison) allows for more accurate and compatible personality pairings.

7. **Temperament Compatibility Rules**: Implementing the adjacency rules for temperament compatibility (where Choleric pairs with Sanguine/Melancholic, Sanguine with Choleric/Phlegmatic, etc.) is crucial for accurate personality profiling.

8. **Presentation Consistency**: UI presentation must adapt to different assessment phases, including varying numbers of answer options while maintaining a consistent user experience.

9. **Question Generation Strategy**: Using different strategies for generating questions in each assessment phase significantly improves accuracy of personality determination.

These insights should inform future development decisions and help avoid common pitfalls in the Harmona project.
