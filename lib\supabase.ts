import { createClient } from '@supabase/supabase-js';

// Get environment variables with fallbacks to prevent URL construction errors
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || '';

// Validate environment variables before creating client
if (!supabaseUrl || !supabaseAnonKey) {
  console.warn('Supabase environment variables not found. Using demo mode.');
  console.warn('To enable full functionality, please:');
  console.warn('1. Create a Supabase project at https://supabase.com');
  console.warn('2. Add your credentials to the .env file');
}

// Create a mock client for demo purposes when Supabase isn't configured
const createMockClient = () => ({
  auth: {
    getSession: () => Promise.resolve({ data: { session: null }, error: null }),
    getUser: () => Promise.resolve({ data: { user: null }, error: null }),
    signInWithPassword: () => Promise.resolve({ data: { user: null, session: null }, error: { message: 'Demo mode - Supabase not configured' } }),
    signUp: () => Promise.resolve({ data: { user: null, session: null }, error: { message: 'Demo mode - Supabase not configured' } }),
    signOut: () => Promise.resolve({ error: null }),
    resetPasswordForEmail: () => Promise.resolve({ data: {}, error: { message: 'Demo mode - Supabase not configured' } }),
  },
  from: () => ({
    select: () => ({
      eq: () => ({
        single: () => Promise.resolve({ data: null, error: { message: 'Demo mode - Supabase not configured' } }),
        order: () => ({
          limit: () => ({
            single: () => Promise.resolve({ data: null, error: { message: 'Demo mode - Supabase not configured' } })
          })
        })
      }),
      order: () => Promise.resolve({ data: [], error: null })
    }),
    insert: () => Promise.resolve({ data: null, error: { message: 'Demo mode - Supabase not configured' } }),
    upsert: () => Promise.resolve({ data: null, error: { message: 'Demo mode - Supabase not configured' } })
  })
});

// Only create client if we have valid environment variables
let supabase: ReturnType<typeof createClient> | any = null;

try {
  if (supabaseUrl && supabaseAnonKey && supabaseUrl !== 'https://your-project-id.supabase.co') {
    supabase = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false,
      },
    });
  } else {
    // Use mock client for demo
    supabase = createMockClient();
  }
} catch (error) {
  console.error('Failed to initialize Supabase client:', error);
  supabase = createMockClient();
}

// Export the client
export { supabase };

// Helper function to check if Supabase is properly configured
export const isSupabaseConfigured = () => {
  return supabaseUrl && 
         supabaseAnonKey && 
         supabaseUrl !== 'https://your-project-id.supabase.co' &&
         supabaseAnonKey !== 'your-anon-key-here';
};

// Helper function to get demo credentials
export const getDemoCredentials = () => ({
  email: '<EMAIL>',
  password: 'demo123456',
  fullName: 'Demo User'
});