import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EmailRequest {
  to: string;
  subject: string;
  html: string;
  verification_code: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { to, subject, html, verification_code }: EmailRequest = await req.json()

    // In a production environment, you would integrate with an email service like:
    // - SendGrid
    // - Mailgun
    // - AWS SES
    // - Resend
    // - Postmark

    // For demo purposes, we'll log the email details
    console.log('📧 Email would be sent to:', to)
    console.log('📧 Subject:', subject)
    console.log('📧 Verification Code:', verification_code)
    console.log('📧 HTML Content:', html)

    // Example integration with Resend (uncomment and configure for production):
    /*
    const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')
    
    if (!RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY is not set')
    }

    const res = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RESEND_API_KEY}`,
      },
      body: JSON.stringify({
        from: 'Harmona <<EMAIL>>',
        to: [to],
        subject: subject,
        html: html,
      }),
    })

    if (!res.ok) {
      const error = await res.text()
      throw new Error(`Failed to send email: ${error}`)
    }

    const data = await res.json()
    */

    // For demo purposes, return success
    const data = { 
      id: 'demo-email-id', 
      message: 'Email sent successfully (demo mode)' 
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        data,
        demo_note: 'In production, this would send a real email. Check console for verification code.' 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    console.error('Error sending email:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message,
        demo_note: 'Email sending failed. In production, configure your email service provider.' 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})