import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  supabase,
  isSupabaseConfigured,
  getDemoCredentials,
} from '../lib/supabase';
import { router } from 'expo-router';

interface User {
  id: string;
  email: string;
  user_metadata: {
    full_name?: string;
  };
  email_confirmed_at?: string;
}

interface Session {
  access_token: string;
  refresh_token: string;
  expires_at: number;
  user: User;
}

interface AuthState {
  // State
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  error: string | null;
  isInitialized: boolean;
  isDemo: boolean;

  // Actions
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, fullName: string) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  getSession: () => Promise<void>;
  clearError: () => void;
  useDemoMode: () => void;
  verifyEmail: (email: string) => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      session: null,
      isLoading: false,
      error: null,
      isInitialized: false,
      isDemo: false,

      getSession: async () => {
        try {
          set({ isLoading: true, error: null });

          if (!isSupabaseConfigured()) {
            set({
              isDemo: true,
              isLoading: false,
              isInitialized: true,
            });
            return;
          }

          const { data, error } = await supabase.auth.getSession();

          if (error) {
            set({
              error: error.message,
              isLoading: false,
              isInitialized: true,
            });
            return;
          }

          set({
            user: data.session?.user || null,
            session: data.session || null,
            isLoading: false,
            isInitialized: true,
          });
        } catch (error: any) {
          set({
            error: error.message || 'Error getting session',
            isLoading: false,
            isInitialized: true,
          });
        }
      },

      signIn: async (email, password) => {
        if (!email || !password) {
          set({ error: 'Please fill in all fields' });
          return;
        }

        try {
          set({ isLoading: true, error: null });

          if (!isSupabaseConfigured()) {
            set({
              isDemo: true,
              isLoading: false,
              error: 'Authentication is not available in demo mode',
            });
            return;
          }

          const { data, error: authError } =
            await supabase.auth.signInWithPassword({
              email,
              password,
            });

          if (authError) {
            // Handle specific authentication errors
            if (authError.message.includes('Invalid login credentials')) {
              set({
                error:
                  'Invalid email or password. Please check your credentials and try again.',
                isLoading: false,
              });
              return;
            } else if (authError.message.includes('Email not confirmed')) {
              set({
                error:
                  'Please verify your email address before signing in. Check your inbox for a verification link.',
                isLoading: false,
              });
              return;
            } else {
              set({
                error: authError.message,
                isLoading: false,
              });
              return;
            }
          }

          if (data.user && data.session) {
            set({
              user: data.user,
              session: data.session,
              isLoading: false,
            });

            // Navigate to the splash screen which will handle proper routing
            router.replace('/');
          }
        } catch (error: any) {
          set({
            error: error.message || 'Login failed. Please try again.',
            isLoading: false,
          });
        }
      },

      signUp: async (email, password, fullName) => {
        if (!email || !password || !fullName) {
          set({ error: 'Please fill in all fields' });
          return;
        }

        try {
          set({ isLoading: true, error: null });

          if (!isSupabaseConfigured()) {
            set({
              isDemo: true,
              isLoading: false,
              error: 'Registration is not available in demo mode',
            });
            return;
          }

          const { data, error } = await supabase.auth.signUp({
            email,
            password,
            options: {
              data: {
                full_name: fullName,
              },
            },
          });

          if (error) {
            set({
              error: error.message,
              isLoading: false,
            });
            return;
          }

          // Check if email confirmation is required
          if (data.user && !data.user.email_confirmed_at) {
            set({
              isLoading: false,
              user: null,
              session: null,
            });

            // Navigate to verification screen
            router.replace({
              pathname: '/auth/verify-email',
              params: { email },
            });
          } else if (data.user && data.session) {
            set({
              user: data.user,
              session: data.session,
              isLoading: false,
            });

            // Navigate to splash screen which will route to assessment
            router.replace('/');
          }
        } catch (error: any) {
          set({
            error: error.message || 'Registration failed. Please try again.',
            isLoading: false,
          });
        }
      },

      signOut: async () => {
        try {
          set({ isLoading: true, error: null });

          if (get().isDemo) {
            set({
              user: null,
              session: null,
              isDemo: false,
              isLoading: false,
            });
            router.replace('/auth');
            return;
          }

          const { error } = await supabase.auth.signOut();

          if (error) {
            set({
              error: error.message,
              isLoading: false,
            });
            return;
          }

          set({
            user: null,
            session: null,
            isLoading: false,
          });

          router.replace('/auth');
        } catch (error: any) {
          set({
            error: error.message || 'Sign out failed',
            isLoading: false,
          });
        }
      },

      resetPassword: async (email) => {
        if (!email) {
          set({ error: 'Please enter your email address' });
          return;
        }

        try {
          set({ isLoading: true, error: null });

          if (!isSupabaseConfigured()) {
            set({
              isLoading: false,
              error: 'Password reset is not available in demo mode',
            });
            return;
          }

          const { error } = await supabase.auth.resetPasswordForEmail(email);

          if (error) {
            set({
              error: error.message,
              isLoading: false,
            });
            return;
          }

          set({
            isLoading: false,
            error: null,
          });

          // Success message handled by component
        } catch (error: any) {
          set({
            error: error.message || 'Password reset failed',
            isLoading: false,
          });
        }
      },

      verifyEmail: async (email) => {
        try {
          set({ isLoading: true, error: null });

          if (!isSupabaseConfigured()) {
            set({
              isLoading: false,
              error: 'Email verification is not available in demo mode',
            });
            return;
          }

          // Trigger Supabase Edge Function to send verification email
          const { data, error } = await supabase.functions.invoke(
            'send-verification-email',
            {
              body: { email },
            }
          );

          if (error) {
            set({
              error: error.message,
              isLoading: false,
            });
            return;
          }

          set({ isLoading: false });
        } catch (error: any) {
          set({
            error: error.message || 'Failed to send verification email',
            isLoading: false,
          });
        }
      },

      clearError: () => set({ error: null }),

      useDemoMode: () => {
        const demo = getDemoCredentials();
        set({
          isDemo: true,
          user: {
            id: 'demo-user-id',
            email: demo.email,
            user_metadata: {
              full_name: demo.fullName,
            },
          },
          session: {
            access_token: 'demo-token',
            refresh_token: 'demo-refresh',
            expires_at: Date.now() + 3600000,
            user: {
              id: 'demo-user-id',
              email: demo.email,
              user_metadata: {
                full_name: demo.fullName,
              },
            },
          },
        });

        // Navigate to the splash screen which will handle routing to the main app
        router.replace('/');
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        session: state.session,
        isDemo: state.isDemo,
      }),
    }
  )
);

// Initialize auth state on app load
export const initializeAuth = async () => {
  const { getSession } = useAuthStore.getState();
  await getSession();
};
