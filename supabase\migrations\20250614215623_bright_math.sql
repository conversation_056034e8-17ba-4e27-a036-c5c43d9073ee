/*
  # Create traits table for dynamic personality traits

  1. New Tables
    - `traits`
      - `id` (uuid, primary key)
      - `temperament` (text, references temperament types)
      - `trait_name` (text, the trait keyword)
      - `description` (text, optional description)
      - `priority` (integer, for ordering traits by importance)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on `traits` table
    - Add policy for authenticated users to read traits
*/

-- Create traits table
CREATE TABLE IF NOT EXISTS traits (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  temperament text NOT NULL,
  trait_name text NOT NULL,
  description text,
  priority integer DEFAULT 1,
  created_at timestamptz DEFAULT now()
);

-- Add constraints
ALTER TABLE traits 
ADD CONSTRAINT traits_temperament_check 
CHECK (temperament = ANY (ARRAY['choleric'::text, 'sanguine'::text, 'melancholic'::text, 'phlegmatic'::text]));

ALTER TABLE traits 
ADD CONSTRAINT traits_priority_check 
CHECK (priority >= 1 AND priority <= 10);

-- Enable RLS
ALTER TABLE traits ENABLE ROW LEVEL SECURITY;

-- Create policy for reading traits
CREATE POLICY "Authenticated users can read traits"
  ON traits
  FOR SELECT
  TO authenticated
  USING (true);

-- Insert traits for Choleric temperament (ordered by priority)
INSERT INTO traits (temperament, trait_name, description, priority) VALUES
('choleric', 'Leader', 'Natural leadership abilities and taking charge', 1),
('choleric', 'Decisive', 'Quick decision-making and clear choices', 2),
('choleric', 'Ambitious', 'Goal-oriented and driven to achieve', 3),
('choleric', 'Direct', 'Straightforward communication style', 4),
('choleric', 'Confident', 'Self-assured and bold in approach', 5),
('choleric', 'Competitive', 'Thrives in competitive environments', 6),
('choleric', 'Independent', 'Prefers autonomy and self-direction', 7),
('choleric', 'Results-focused', 'Emphasizes outcomes and achievements', 8);

-- Insert traits for Sanguine temperament (ordered by priority)
INSERT INTO traits (temperament, trait_name, description, priority) VALUES
('sanguine', 'Enthusiastic', 'High energy and excitement for life', 1),
('sanguine', 'Social', 'Loves interaction and building relationships', 2),
('sanguine', 'Optimistic', 'Positive outlook and hopeful perspective', 3),
('sanguine', 'Spontaneous', 'Flexible and adaptable to change', 4),
('sanguine', 'Expressive', 'Open with emotions and thoughts', 5),
('sanguine', 'Charismatic', 'Naturally attracts and influences others', 6),
('sanguine', 'Creative', 'Innovative and imaginative thinking', 7),
('sanguine', 'Energetic', 'High activity level and vitality', 8);

-- Insert traits for Melancholic temperament (ordered by priority)
INSERT INTO traits (temperament, trait_name, description, priority) VALUES
('melancholic', 'Thoughtful', 'Deep thinking and careful consideration', 1),
('melancholic', 'Analytical', 'Systematic analysis and logical reasoning', 2),
('melancholic', 'Creative', 'Artistic and innovative expression', 3),
('melancholic', 'Perfectionist', 'High standards and attention to detail', 4),
('melancholic', 'Introspective', 'Self-reflective and contemplative', 5),
('melancholic', 'Sensitive', 'Emotionally aware and empathetic', 6),
('melancholic', 'Organized', 'Structured and methodical approach', 7),
('melancholic', 'Loyal', 'Committed and faithful in relationships', 8);

-- Insert traits for Phlegmatic temperament (ordered by priority)
INSERT INTO traits (temperament, trait_name, description, priority) VALUES
('phlegmatic', 'Calm', 'Peaceful and composed demeanor', 1),
('phlegmatic', 'Loyal', 'Faithful and committed to relationships', 2),
('phlegmatic', 'Peaceful', 'Harmony-seeking and conflict-avoiding', 3),
('phlegmatic', 'Patient', 'Tolerant and understanding with others', 4),
('phlegmatic', 'Supportive', 'Helpful and encouraging to others', 5),
('phlegmatic', 'Reliable', 'Consistent and dependable', 6),
('phlegmatic', 'Diplomatic', 'Tactful and considerate in communication', 7),
('phlegmatic', 'Steady', 'Stable and consistent in behavior', 8);

-- Create function to get mixed personality traits based on percentages
CREATE OR REPLACE FUNCTION get_mixed_personality_traits(
  dominant_temp text,
  secondary_temp text,
  dominant_pct integer,
  secondary_pct integer
)
RETURNS TABLE(trait_name text, temperament text, description text)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  dominant_count integer;
  secondary_count integer;
BEGIN
  -- Calculate trait distribution based on percentages
  -- For 60% dominant, show 3 traits; for 40% secondary, show 2 traits
  -- Scale proportionally for other percentages
  dominant_count := GREATEST(1, LEAST(5, ROUND(dominant_pct / 20.0)));
  secondary_count := GREATEST(0, LEAST(4, ROUND(secondary_pct / 20.0)));
  
  -- Ensure we don't exceed 5 total traits
  IF dominant_count + secondary_count > 5 THEN
    secondary_count := 5 - dominant_count;
  END IF;

  -- Return dominant temperament traits first
  RETURN QUERY
  SELECT t.trait_name, t.temperament, t.description
  FROM traits t
  WHERE t.temperament = dominant_temp
  ORDER BY t.priority
  LIMIT dominant_count;

  -- Return secondary temperament traits
  IF secondary_count > 0 THEN
    RETURN QUERY
    SELECT t.trait_name, t.temperament, t.description
    FROM traits t
    WHERE t.temperament = secondary_temp
    ORDER BY t.priority
    LIMIT secondary_count;
  END IF;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_mixed_personality_traits(text, text, integer, integer) TO authenticated;