import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  ArrowLeft,
  ArrowRight,
  <PERSON>rkles,
  RefreshCw,
  TriangleAlert as Alert<PERSON>riangle,
} from 'lucide-react-native';
import { useAssessmentStore } from '@/stores';
import { openRouterService } from '@/lib/openrouter';

const TEMPERAMENT_MAPPING = [
  'choleric',
  'sanguine',
  'melancholic',
  'phlegmatic',
];

export default function AssessmentScreen() {
  // Get assessment store state and actions
  const {
    questions,
    currentQuestionIndex,
    responses,
    selectedAnswer,
    phase,
    isLoading,
    isGeneratingQuestions,
    isSubmitting,
    error,
    aiError,
    isRetake,
    useAI,

    // Assessment store actions
    initializeAssessment,
    handleAnswerSelect,
    handleNextQuestion,
    handlePreviousQuestion,
    regenerateQuestions,
    useFallbackQuestions,
    getProgressPercentage,
  } = useAssessmentStore();

  const mounted = useRef(true);

  useEffect(() => {
    mounted.current = true;
    initializeAssessment();

    return () => {
      mounted.current = false;
    };
  }, []);

  // Show loading state
  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text style={styles.loadingText}>Loading assessment...</Text>
          {isRetake && (
            <Text style={styles.retakeText}>
              Preparing fresh questions for retake
            </Text>
          )}
        </View>
      </SafeAreaView>
    );
  }

  // Show question generation state
  if (isGeneratingQuestions) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Sparkles size={48} color="#3B82F6" />
          <Text style={styles.loadingTitle}>
            {useAI ? 'Generating Fresh AI Questions' : 'Loading Questions'}
          </Text>
          <Text style={styles.loadingText}>
            {useAI
              ? 'Creating unique questions with mixed answer positions...'
              : 'Preparing your assessment...'}
          </Text>
          {isRetake && (
            <Text style={styles.retakeText}>
              This will replace your previous results
            </Text>
          )}
          <ActivityIndicator
            size="large"
            color="#3B82F6"
            style={styles.loader}
          />
        </View>
      </SafeAreaView>
    );
  }

  // Show error state
  if (error && !questions.length) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <AlertTriangle size={48} color="#EF4444" />
          <Text style={styles.errorTitle}>Something went wrong</Text>
          <Text style={styles.errorText}>{error}</Text>
          <View style={styles.errorButtons}>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => {
                initializeAssessment();
              }}
            >
              <Text style={styles.retryButtonText}>Try Again</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.fallbackButton}
              onPress={useFallbackQuestions}
            >
              <Text style={styles.fallbackButtonText}>
                Use Standard Questions
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  if (!questions.length) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <AlertTriangle size={48} color="#EF4444" />
          <Text style={styles.errorTitle}>No questions available</Text>
          <Text style={styles.errorText}>
            Unable to load assessment questions. Please try again.
          </Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => {
              initializeAssessment();
            }}
          >
            <Text style={styles.retryButtonText}>Reload</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const progress = getProgressPercentage();
  const currentQ = questions[currentQuestionIndex];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={handlePreviousQuestion}
          disabled={currentQuestionIndex === 0}
          style={[
            styles.navButton,
            currentQuestionIndex === 0 && styles.navButtonDisabled,
          ]}
        >
          <ArrowLeft
            size={24}
            color={currentQuestionIndex === 0 ? '#9CA3AF' : '#1F2937'}
          />
        </TouchableOpacity>

        <View style={styles.progressContainer}>
          <View style={styles.progressHeader}>
            <Text style={styles.progressText}>
              {currentQuestionIndex + 1} of {questions.length}
            </Text>
            {openRouterService.isConfigured() && (
              <TouchableOpacity
                style={styles.regenerateButton}
                onPress={regenerateQuestions}
                disabled={isGeneratingQuestions}
              >
                <RefreshCw size={16} color="#3B82F6" />
                <Text style={styles.regenerateText}>Fresh Questions</Text>
              </TouchableOpacity>
            )}
          </View>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: `${progress}%` }]} />
          </View>
          <View style={styles.indicatorContainer}>
            {isRetake && (
              <View style={styles.retakeIndicator}>
                <RefreshCw size={12} color="#F59E0B" />
                <Text style={styles.retakeIndicatorText}>Retake</Text>
              </View>
            )}
            {useAI && !aiError && openRouterService.isConfigured() && (
              <View style={styles.aiIndicator}>
                <Sparkles size={12} color="#3B82F6" />
                <Text style={styles.aiText}>AI-Generated</Text>
              </View>
            )}
            {aiError && (
              <View style={styles.fallbackIndicator}>
                <AlertTriangle size={12} color="#F59E0B" />
                <Text style={styles.fallbackText}>Standard Questions</Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.navButton} />
      </View>

      {isRetake && (
        <View style={styles.retakeBanner}>
          <RefreshCw size={16} color="#F59E0B" />
          <Text style={styles.retakeBannerText}>
            Retaking assessment with fresh questions - this will replace your
            previous personality profile
          </Text>
        </View>
      )}

      {aiError && (
        <View style={styles.aiErrorBanner}>
          <AlertTriangle size={16} color="#F59E0B" />
          <Text style={styles.aiErrorText}>
            AI generation failed: {aiError}. Using enhanced standard questions.
          </Text>
        </View>
      )}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.questionContainer}>
          <View style={styles.categoryBadge}>
            <Text style={styles.categoryText}>
              {currentQ.category.charAt(0).toUpperCase() +
                currentQ.category.slice(1)}
            </Text>
          </View>

          <Text style={styles.question}>{currentQ.question}</Text>

          {currentQ.temperamentOrder && __DEV__ && (
            <Text style={styles.debugText}>
              Debug:{' '}
              {phase === 'comparison' ? (
                /* Comparison phase has only 3 temperaments */
                <>
                  A={TEMPERAMENT_MAPPING[currentQ.temperamentOrder[0]]}, B=
                  {TEMPERAMENT_MAPPING[currentQ.temperamentOrder[1]]}, C=
                  {TEMPERAMENT_MAPPING[currentQ.temperamentOrder[2]]}
                </>
              ) : (
                /* Initial and confirmation phases have all 4 temperaments */
                <>
                  A={TEMPERAMENT_MAPPING[currentQ.temperamentOrder[0]]}, B=
                  {TEMPERAMENT_MAPPING[currentQ.temperamentOrder[1]]}, C=
                  {TEMPERAMENT_MAPPING[currentQ.temperamentOrder[2]]}, D=
                  {TEMPERAMENT_MAPPING[currentQ.temperamentOrder[3]]}
                </>
              )}
            </Text>
          )}
        </View>

        <View style={styles.answersContainer}>
          {/* In comparison phase, we only show 3 answers instead of 4 */}
          {currentQ.answers.map((answer, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.answerButton,
                selectedAnswer === index && styles.selectedAnswer,
              ]}
              onPress={() => handleAnswerSelect(index)}
            >
              <View style={styles.answerContent}>
                <View
                  style={[
                    styles.answerIndicator,
                    selectedAnswer === index && styles.selectedIndicator,
                  ]}
                >
                  <Text
                    style={[
                      styles.answerLetter,
                      selectedAnswer === index && styles.selectedAnswerLetter,
                    ]}
                  >
                    {String.fromCharCode(65 + index)}
                  </Text>
                </View>
                <Text
                  style={[
                    styles.answerText,
                    selectedAnswer === index && styles.selectedAnswerText,
                  ]}
                >
                  {answer}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.nextButton,
            (selectedAnswer === null || isSubmitting) &&
              styles.nextButtonDisabled,
          ]}
          onPress={handleNextQuestion}
          disabled={selectedAnswer === null || isSubmitting}
        >
          <Text style={styles.nextButtonText}>
            {isSubmitting
              ? isRetake
                ? 'Updating Profile...'
                : 'Submitting...'
              : currentQuestionIndex === questions.length - 1
              ? isRetake
                ? 'Update Assessment'
                : 'Complete Assessment'
              : 'Next Question'}
          </Text>
          {!isSubmitting && <ArrowRight size={20} color="#ffffff" />}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    paddingHorizontal: 24,
  },
  loadingTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'center',
    marginTop: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  retakeText: {
    fontSize: 14,
    color: '#F59E0B',
    textAlign: 'center',
    fontWeight: '500',
  },
  loader: {
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
    gap: 16,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#EF4444',
    textAlign: 'center',
    marginTop: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  errorButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  retryButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#ffffff',
    fontWeight: '600',
    fontSize: 16,
  },
  fallbackButton: {
    backgroundColor: '#F59E0B',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  fallbackButtonText: {
    color: '#ffffff',
    fontWeight: '600',
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  navButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  navButtonDisabled: {
    opacity: 0.3,
  },
  progressContainer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    marginBottom: 8,
  },
  progressText: {
    fontSize: 14,
    color: '#6B7280',
  },
  regenerateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    backgroundColor: '#EBF8FF',
  },
  regenerateText: {
    fontSize: 12,
    color: '#3B82F6',
    fontWeight: '500',
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#3B82F6',
    borderRadius: 2,
  },
  indicatorContainer: {
    minHeight: 16,
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  retakeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  retakeIndicatorText: {
    fontSize: 10,
    color: '#F59E0B',
    fontWeight: '500',
  },
  aiIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  aiText: {
    fontSize: 10,
    color: '#3B82F6',
    fontWeight: '500',
  },
  fallbackIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  fallbackText: {
    fontSize: 10,
    color: '#F59E0B',
    fontWeight: '500',
  },
  retakeBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
  },
  retakeBannerText: {
    flex: 1,
    fontSize: 14,
    color: '#92400E',
    fontWeight: '500',
  },
  aiErrorBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
  },
  aiErrorText: {
    flex: 1,
    fontSize: 14,
    color: '#92400E',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  questionContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  categoryBadge: {
    backgroundColor: '#EBF8FF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginBottom: 16,
  },
  categoryText: {
    fontSize: 12,
    color: '#3B82F6',
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  question: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'center',
    lineHeight: 32,
  },
  debugText: {
    fontSize: 10,
    color: '#9CA3AF',
    textAlign: 'center',
    marginTop: 8,
    fontFamily: 'monospace',
  },
  answersContainer: {
    gap: 16,
    paddingBottom: 32,
  },
  answerButton: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedAnswer: {
    backgroundColor: '#EBF8FF',
    borderColor: '#3B82F6',
  },
  answerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    gap: 16,
  },
  answerIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedIndicator: {
    backgroundColor: '#3B82F6',
  },
  answerLetter: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  selectedAnswerLetter: {
    color: '#ffffff',
  },
  answerText: {
    flex: 1,
    fontSize: 16,
    color: '#4B5563',
    lineHeight: 24,
  },
  selectedAnswerText: {
    color: '#1E40AF',
    fontWeight: '500',
  },
  footer: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  nextButton: {
    backgroundColor: '#3B82F6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  nextButtonDisabled: {
    opacity: 0.5,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});
