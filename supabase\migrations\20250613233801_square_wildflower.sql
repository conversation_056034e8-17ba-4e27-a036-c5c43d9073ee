-- Migration: Update subgreetings table to use emoji moods
-- This migration safely updates the mood constraint and adds new emoji-based subgreetings

-- Step 1: Clear all existing subgreetings to avoid constraint conflicts
TRUNCATE TABLE subgreetings;

-- Step 2: Drop the existing mood constraint
ALTER TABLE subgreetings DROP CONSTRAINT IF EXISTS subgreetings_mood_check;

-- Step 3: Add the new constraint with emoji support
ALTER TABLE subgreetings 
ADD CONSTRAINT subgreetings_mood_check 
CHECK (mood = ANY (ARRAY['😄'::text, '🙂'::text, '😐'::text, '😕'::text, '😢'::text, 'neutral'::text]));

-- Step 4: Insert new subgreetings for Choleric temperament with emoji moods
INSERT INTO subgreetings (temperament, message, mood, time_of_day) VALUES
-- Very Happy (😄) - Energetic and triumphant
('choleric', 'absolutely crushing your goals today', '😄', 'morning'),
('choleric', 'on fire and unstoppable', '😄', 'any'),
('choleric', 'dominating every challenge that comes your way', '😄', 'afternoon'),
('choleric', 'celebrating another victory', '😄', 'evening'),

-- Happy (🙂) - Confident and driven
('choleric', 'ready to conquer your goals today', '🙂', 'morning'),
('choleric', 'in full leadership mode', '🙂', 'any'),
('choleric', 'channeling your natural determination', '🙂', 'any'),
('choleric', 'focused on achieving excellence', '🙂', 'afternoon'),
('choleric', 'embracing your decisive nature', '🙂', 'evening'),

-- Neutral (😐) - Steady and focused
('choleric', 'maintaining your strategic focus', '😐', 'any'),
('choleric', 'staying committed to your objectives', '😐', 'afternoon'),
('choleric', 'keeping your eyes on the prize', '😐', 'any'),

-- Sad (😕) - Determined despite challenges
('choleric', 'pushing through with unwavering resolve', '😕', 'any'),
('choleric', 'turning setbacks into comebacks', '😕', 'afternoon'),
('choleric', 'finding strength in adversity', '😕', 'evening'),

-- Very Sad (😢) - Resilient and rebuilding
('choleric', 'rebuilding with even greater determination', '😢', 'any'),
('choleric', 'using this moment to fuel your comeback', '😢', 'evening');

-- Insert new subgreetings for Sanguine temperament with emoji moods
INSERT INTO subgreetings (temperament, message, mood, time_of_day) VALUES
-- Very Happy (😄) - Extremely social and joyful
('sanguine', 'absolutely radiating pure joy and excitement', '😄', 'morning'),
('sanguine', 'spreading infectious happiness everywhere', '😄', 'any'),
('sanguine', 'lighting up the world with your energy', '😄', 'afternoon'),
('sanguine', 'celebrating life in the most beautiful way', '😄', 'evening'),

-- Happy (🙂) - Optimistic and social
('sanguine', 'radiating positive energy and enthusiasm', '🙂', 'morning'),
('sanguine', 'bringing joy to everyone around you', '🙂', 'any'),
('sanguine', 'spreading optimism wherever you go', '🙂', 'any'),
('sanguine', 'connecting hearts and building friendships', '🙂', 'afternoon'),
('sanguine', 'inspiring others with your zest for life', '🙂', 'evening'),

-- Neutral (😐) - Balanced but still social
('sanguine', 'finding your social rhythm today', '😐', 'any'),
('sanguine', 'staying connected with those who matter', '😐', 'afternoon'),
('sanguine', 'maintaining your natural warmth', '😐', 'any'),

-- Sad (😕) - Seeking comfort through connection
('sanguine', 'finding comfort in meaningful connections', '😕', 'any'),
('sanguine', 'letting friends lift your spirits', '😕', 'afternoon'),
('sanguine', 'remembering that brighter days are ahead', '😕', 'evening'),

-- Very Sad (😢) - Needing support and understanding
('sanguine', 'allowing yourself to feel and heal', '😢', 'any'),
('sanguine', 'finding strength in the love around you', '😢', 'evening');

-- Insert new subgreetings for Melancholic temperament with emoji moods
INSERT INTO subgreetings (temperament, message, mood, time_of_day) VALUES
-- Very Happy (😄) - Creatively inspired and fulfilled
('melancholic', 'experiencing pure creative bliss', '😄', 'morning'),
('melancholic', 'feeling deeply fulfilled and inspired', '😄', 'any'),
('melancholic', 'creating something truly magnificent', '😄', 'afternoon'),
('melancholic', 'finding perfect harmony in your thoughts', '😄', 'evening'),

-- Happy (🙂) - Thoughtful and creative
('melancholic', 'diving deep into thoughtful reflection', '🙂', 'morning'),
('melancholic', 'appreciating the beauty in details', '🙂', 'any'),
('melancholic', 'channeling your analytical brilliance', '🙂', 'any'),
('melancholic', 'expressing your rich inner world', '🙂', 'afternoon'),
('melancholic', 'connecting with deeper truths and meanings', '🙂', 'evening'),

-- Neutral (😐) - Contemplative and steady
('melancholic', 'processing thoughts with quiet wisdom', '😐', 'any'),
('melancholic', 'finding clarity in contemplation', '😐', 'afternoon'),
('melancholic', 'maintaining your thoughtful perspective', '😐', 'any'),

-- Sad (😕) - Introspective and seeking meaning
('melancholic', 'finding meaning in difficult moments', '😕', 'any'),
('melancholic', 'using introspection to understand deeper', '😕', 'afternoon'),
('melancholic', 'transforming pain into profound insight', '😕', 'evening'),

-- Very Sad (😢) - Deeply feeling and processing
('melancholic', 'honoring the depth of your emotions', '😢', 'any'),
('melancholic', 'finding beauty even in sorrow', '😢', 'evening');

-- Insert new subgreetings for Phlegmatic temperament with emoji moods
INSERT INTO subgreetings (temperament, message, mood, time_of_day) VALUES
-- Very Happy (😄) - Peacefully joyful and harmonious
('phlegmatic', 'radiating peaceful joy and contentment', '😄', 'morning'),
('phlegmatic', 'spreading harmony and happiness', '😄', 'any'),
('phlegmatic', 'creating beautiful moments of serenity', '😄', 'afternoon'),
('phlegmatic', 'feeling perfectly balanced and joyful', '😄', 'evening'),

-- Happy (🙂) - Calm and supportive
('phlegmatic', 'bringing peace and harmony to your surroundings', '🙂', 'morning'),
('phlegmatic', 'being the steady anchor others rely on', '🙂', 'any'),
('phlegmatic', 'creating a sense of comfort and stability', '🙂', 'any'),
('phlegmatic', 'nurturing relationships with gentle care', '🙂', 'afternoon'),
('phlegmatic', 'offering support with your loyal heart', '🙂', 'evening'),

-- Neutral (😐) - Steady and reliable
('phlegmatic', 'maintaining your natural equilibrium', '😐', 'any'),
('phlegmatic', 'providing steady support to others', '😐', 'afternoon'),
('phlegmatic', 'staying grounded and centered', '😐', 'any'),

-- Sad (😕) - Quietly supportive despite struggles
('phlegmatic', 'finding strength in your gentle nature', '😕', 'any'),
('phlegmatic', 'offering quiet comfort to yourself and others', '😕', 'afternoon'),
('phlegmatic', 'maintaining peace even in difficult times', '😕', 'evening'),

-- Very Sad (😢) - Seeking and providing comfort
('phlegmatic', 'allowing yourself to rest and heal', '😢', 'any'),
('phlegmatic', 'finding solace in quiet moments', '😢', 'evening');

-- Create function to get user's latest mood for personalized greetings
CREATE OR REPLACE FUNCTION get_user_latest_mood(user_uuid uuid)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    latest_mood text;
BEGIN
    SELECT emoji INTO latest_mood
    FROM mood_entries
    WHERE user_id = user_uuid
    ORDER BY created_at DESC
    LIMIT 1;
    
    RETURN COALESCE(latest_mood, 'neutral');
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_user_latest_mood(uuid) TO authenticated;