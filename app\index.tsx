import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { supabase, isSupabaseConfigured } from '@/lib/supabase';
import { useAuthStore } from '@/stores';
import { LinearGradient } from 'expo-linear-gradient';

export default function SplashScreen() {
  const [debugInfo, setDebugInfo] = useState('Initializing...');
  const [hasNavigated, setHasNavigated] = useState(false);
  const { user, isInitialized, isDemo } = useAuthStore();

  useEffect(() => {
    let mounted = true;

    const checkAuthStatus = async () => {
      if (!mounted || !isInitialized || hasNavigated) {
        return;
      }

      console.log('🔍 Splash screen auth check:', {
        userId: user?.id,
        isDemo,
        isInitialized,
        hasNavigated,
      });

      try {
        // Check if user is authenticated
        if (user) {
          setDebugInfo(`User found: ${user.email}`);

          // Give a moment for any database operations to complete
          await new Promise((resolve) => setTimeout(resolve, 500));

          if (!mounted) return;

          setDebugInfo('Checking user profile...');

          try {
            // Try to get user profile with retries
            let profile = null;
            let attempts = 0;
            const maxAttempts = 3;

            while (attempts < maxAttempts && !profile && mounted) {
              attempts++;
              setDebugInfo(
                `Fetching profile (attempt ${attempts}/${maxAttempts})...`
              );

              const { data: profiles, error: profileError } = await supabase
                .from('user_profiles')
                .select('has_completed_assessment')
                .eq('id', user.id);

              if (profileError) {
                console.error(
                  `Profile fetch error (attempt ${attempts}):`,
                  profileError
                );
                if (attempts < maxAttempts) {
                  await new Promise((resolve) => setTimeout(resolve, 1000));
                  continue;
                }
              } else {
                profile = profiles && profiles.length > 0 ? profiles[0] : null;
                break;
              }
            }

            if (!mounted) return;

            // If no profile found after retries, create one
            if (!profile) {
              console.log('No profile found, creating one...');
              setDebugInfo('Creating user profile...');

              const { error: createError } = await supabase
                .from('user_profiles')
                .insert({
                  id: user.id,
                  full_name: user.user_metadata?.full_name || '',
                  email: user.email || '',
                  has_completed_assessment: false,
                });

              if (createError) {
                console.error('Error creating profile:', createError);
                setDebugInfo(`Profile creation failed: ${createError.message}`);

                // If we can't create a profile, sign out and redirect
                await useAuthStore.getState().signOut();
                return;
              }

              // Set default profile values for new user
              profile = {
                has_completed_assessment: false,
              };
            }

            if (!mounted) return;

            // Route based on profile status
            console.log('Profile status:', profile);
            setDebugInfo('Determining next screen...');

            // Add a small delay before navigation to ensure smooth transition
            await new Promise((resolve) => setTimeout(resolve, 500));

            if (!mounted) return;

            // Check if assessment is actually completed by looking for personality profile
            if (!profile.has_completed_assessment) {
              console.log(
                'Profile says assessment not completed, checking for personality profile...'
              );

              // Check if personality profile exists (assessment might be completed but flag not updated)
              const { data: personalityProfile, error: personalityError } =
                await supabase
                  .from('personality_profiles')
                  .select('id')
                  .eq('user_id', user.id)
                  .single();

              if (personalityProfile && !personalityError) {
                console.log(
                  '✅ Personality profile exists, updating has_completed_assessment flag'
                );

                // Update the profile flag
                await supabase
                  .from('user_profiles')
                  .update({ has_completed_assessment: true })
                  .eq('id', user.id);

                profile.has_completed_assessment = true;
              }
            }

            if (profile.has_completed_assessment) {
              console.log('Assessment completed, redirecting to tabs');
              setDebugInfo('Redirecting to main app...');
              setHasNavigated(true);
              router.replace('/(tabs)');
            } else {
              console.log(
                'Assessment not completed, redirecting to assessment'
              );
              setDebugInfo('Redirecting to assessment...');
              setHasNavigated(true);
              router.replace('/assessment');
            }
          } catch (error) {
            console.error('Profile handling error:', error);
            setDebugInfo(`Profile error: ${error}`);
            // Clear any invalid session tokens before redirecting
            await useAuthStore.getState().signOut();
          }
        } else {
          console.log('No session found, redirecting to auth');
          setDebugInfo('No user session found');

          // For demo mode we might have a user but no real session
          if (isDemo) {
            setDebugInfo('Demo mode, redirecting to main app...');
            setHasNavigated(true);
            setTimeout(() => {
              router.replace('/(tabs)');
            }, 1000);
          } else {
            setHasNavigated(true);
            setTimeout(() => {
              router.replace('/auth');
            }, 1000);
          }
        }
      } catch (error) {
        console.error('Auth check error:', error);
        setDebugInfo(`Auth error: ${error}`);
        // Clear any invalid session tokens before redirecting
        await useAuthStore.getState().signOut();
      }
    };

    // Start the auth check after a brief delay
    const timer = setTimeout(checkAuthStatus, 300);

    return () => {
      mounted = false;
      clearTimeout(timer);
    };
  }, [user?.id, isInitialized, isDemo, hasNavigated]);

  return (
    <LinearGradient colors={['#3B82F6', '#1D4ED8']} style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Harmona</Text>
        <Text style={styles.tagline}>Understand Yourself. Connect Better.</Text>
        {!isInitialized ? (
          <ActivityIndicator
            size="large"
            color="#ffffff"
            style={styles.loader}
          />
        ) : (
          <Text style={styles.debugText}>{debugInfo}</Text>
        )}
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  title: {
    fontSize: 48,
    fontWeight: '700',
    color: '#ffffff',
    marginBottom: 8,
  },
  tagline: {
    fontSize: 18,
    color: '#E5E7EB',
    textAlign: 'center',
    marginBottom: 32,
  },
  loader: {
    marginTop: 32,
  },
  debugText: {
    fontSize: 14,
    color: '#E5E7EB',
    textAlign: 'center',
    marginTop: 16,
    opacity: 0.8,
  },
});
