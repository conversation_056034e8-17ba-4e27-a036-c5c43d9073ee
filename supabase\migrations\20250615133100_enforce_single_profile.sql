-- Migration to enforce single personality profile per user
-- This adds a unique constraint to prevent multiple profiles for a single user

-- First drop any potential duplicates
CREATE OR REPLACE FUNCTION cleanup_duplicate_profiles() RETURNS void AS $$
DECLARE
    user_rec RECORD;
    profile_count INTEGER;
    profiles_to_keep UUID[];
    profiles_to_delete UUID[];
BEGIN
    -- Find all users with multiple profiles
    FOR user_rec IN 
        SELECT user_id 
        FROM personality_profiles 
        GROUP BY user_id 
        HAVING COUNT(*) > 1
    LOOP
        RAISE NOTICE 'Cleaning up profiles for user %', user_rec.user_id;
        
        -- Keep only the most recently created profile
        profiles_to_keep := ARRAY(
            SELECT id FROM personality_profiles
            WHERE user_id = user_rec.user_id
            ORDER BY created_at DESC NULLS LAST
            LIMIT 1
        );
        
        -- Delete all other profiles
        profiles_to_delete := ARRAY(
            SELECT id FROM personality_profiles
            WHERE user_id = user_rec.user_id
            AND id <> ALL(profiles_to_keep)
        );
        
        RAISE NOTICE 'Keeping profile % and deleting %', profiles_to_keep, profiles_to_delete;
        
        -- Delete the duplicate profiles
        DELETE FROM personality_profiles
        WHERE id = ANY(profiles_to_delete);
        
        -- Get the count after cleanup
        SELECT COUNT(*) INTO profile_count
        FROM personality_profiles
        WHERE user_id = user_rec.user_id;
        
        RAISE NOTICE 'After cleanup, user % has % profile(s)', user_rec.user_id, profile_count;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Run the cleanup function
SELECT cleanup_duplicate_profiles();

-- Drop the function after use
DROP FUNCTION cleanup_duplicate_profiles();

-- Add unique constraint to ensure one profile per user
ALTER TABLE personality_profiles
ADD CONSTRAINT personality_profiles_user_id_unique UNIQUE (user_id);

-- Create a stored procedure for force deletion of profiles
-- This allows the client to use one simple call to ensure all profiles are deleted
CREATE OR REPLACE FUNCTION force_delete_personality_profiles(user_id_param UUID) 
RETURNS void AS $$
BEGIN
    -- Delete all profiles for the user
    DELETE FROM personality_profiles
    WHERE user_id = user_id_param;
    
    -- Verify deletion
    IF EXISTS (SELECT 1 FROM personality_profiles WHERE user_id = user_id_param) THEN
        RAISE EXCEPTION 'Failed to delete all profiles for user %', user_id_param;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Add comments to the function for documentation
COMMENT ON FUNCTION force_delete_personality_profiles(UUID) IS 
'Forcefully deletes all personality profiles for a given user ID. 
Used to ensure clean state before creating a new profile.';
