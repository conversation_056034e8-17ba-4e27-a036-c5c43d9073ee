/*
  # Enhanced Circle Reminders with Love Tank Integration

  1. New Tables
    - `circle_love_tanks` - Track love tank levels for each contact
    - `reminder_templates` - Actionable reminder templates
    - `reminder_actions` - Specific actions (text, email, call, etc.)

  2. Enhanced Features
    - Love tank tracking per contact
    - Personality-based reminder generation
    - Specific action suggestions (WhatsApp, email, call)
    - Template-based personalized messages

  3. Security
    - Enable RLS on all new tables
    - Add policies for user data isolation
*/

-- Create circle_love_tanks table to track love tank levels for each contact
CREATE TABLE IF NOT EXISTS circle_love_tanks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  contact_id uuid NOT NULL,
  category_id uuid NOT NULL,
  current_level integer DEFAULT 50 CHECK (current_level >= 0 AND current_level <= 100),
  target_level integer DEFAULT 80 CHECK (target_level >= 0 AND target_level <= 100),
  last_filled_at timestamptz,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(contact_id, category_id) -- One love tank per category per contact
);

-- Create reminder_templates table for actionable reminder templates
CREATE TABLE IF NOT EXISTS reminder_templates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  contact_temperament text NOT NULL,
  love_tank_category text NOT NULL,
  action_type text NOT NULL, -- 'text', 'whatsapp', 'email', 'call', 'in_person', 'gift'
  title_template text NOT NULL,
  message_template text NOT NULL,
  action_suggestion text NOT NULL, -- Specific action to take
  sample_message text, -- Example message to send
  priority integer DEFAULT 1,
  created_at timestamptz DEFAULT now()
);

-- Create reminder_actions table for tracking completed actions
CREATE TABLE IF NOT EXISTS reminder_actions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  contact_id uuid NOT NULL,
  love_tank_category_id uuid NOT NULL,
  action_type text NOT NULL,
  action_taken text, -- What the user actually did
  message_sent text, -- Actual message sent
  completed_at timestamptz DEFAULT now(),
  love_tank_increase integer DEFAULT 0, -- How much the love tank increased
  created_at timestamptz DEFAULT now()
);

-- Add constraints
ALTER TABLE circle_love_tanks 
ADD CONSTRAINT circle_love_tanks_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES user_profiles(id) ON DELETE CASCADE;

ALTER TABLE circle_love_tanks 
ADD CONSTRAINT circle_love_tanks_contact_id_fkey 
FOREIGN KEY (contact_id) REFERENCES circle_contacts(id) ON DELETE CASCADE;

ALTER TABLE circle_love_tanks 
ADD CONSTRAINT circle_love_tanks_category_id_fkey 
FOREIGN KEY (category_id) REFERENCES love_tank_categories(id) ON DELETE CASCADE;

ALTER TABLE reminder_templates 
ADD CONSTRAINT reminder_templates_temperament_check 
CHECK (contact_temperament = ANY (ARRAY['choleric'::text, 'sanguine'::text, 'melancholic'::text, 'phlegmatic'::text, 'universal'::text]));

ALTER TABLE reminder_templates 
ADD CONSTRAINT reminder_templates_action_type_check 
CHECK (action_type = ANY (ARRAY['text'::text, 'whatsapp'::text, 'email'::text, 'call'::text, 'in_person'::text, 'gift'::text, 'activity'::text]));

ALTER TABLE reminder_actions 
ADD CONSTRAINT reminder_actions_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES user_profiles(id) ON DELETE CASCADE;

ALTER TABLE reminder_actions 
ADD CONSTRAINT reminder_actions_contact_id_fkey 
FOREIGN KEY (contact_id) REFERENCES circle_contacts(id) ON DELETE CASCADE;

ALTER TABLE reminder_actions 
ADD CONSTRAINT reminder_actions_category_id_fkey 
FOREIGN KEY (love_tank_category_id) REFERENCES love_tank_categories(id) ON DELETE CASCADE;

-- Enable RLS
ALTER TABLE circle_love_tanks ENABLE ROW LEVEL SECURITY;
ALTER TABLE reminder_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE reminder_actions ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can manage own circle love tanks"
  ON circle_love_tanks
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Anyone can read reminder templates"
  ON reminder_templates
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can manage own reminder actions"
  ON reminder_actions
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Insert comprehensive reminder templates

-- CHOLERIC + ACHIEVEMENT RECOGNITION
INSERT INTO reminder_templates (contact_temperament, love_tank_category, action_type, title_template, message_template, action_suggestion, sample_message, priority) VALUES
('choleric', 'Achievement Recognition', 'text', 'Acknowledge {contact_name}''s Recent Win', 'Send a message recognizing their latest achievement or progress', 'Text them about a specific accomplishment you''ve noticed', 'Hey {contact_name}! I saw you crushed that project deadline. Your leadership and drive are incredible - the team is lucky to have you! 🏆', 1),
('choleric', 'Achievement Recognition', 'email', 'Professional Recognition for {contact_name}', 'Send a professional email highlighting their contributions', 'Write a detailed email about their impact and achievements', 'Subject: Your Outstanding Leadership\n\nHi {contact_name},\n\nI wanted to take a moment to recognize the exceptional work you''ve been doing. Your strategic thinking and decisive leadership on the recent project really made the difference. Thank you for setting such a high standard.\n\nBest regards', 2),
('choleric', 'Achievement Recognition', 'call', 'Victory Call with {contact_name}', 'Call to personally congratulate them on their success', 'Make a quick call to celebrate their achievement', 'Call them and say: "I had to call and tell you how impressed I am with what you accomplished. Your determination really paid off!"', 3),

-- CHOLERIC + AUTONOMY & CONTROL
('choleric', 'Autonomy & Control', 'text', 'Support {contact_name}''s Leadership', 'Message them about trusting their judgment on decisions', 'Text showing you respect their authority and decision-making', 'Hi {contact_name}, I know you''re handling the {project/situation} and I have complete confidence in your judgment. Let me know if you need anything, but I trust you''ve got this! 💪', 1),
('choleric', 'Autonomy & Control', 'email', 'Delegate Authority to {contact_name}', 'Email giving them full control over a project or decision', 'Send an email formally delegating authority or asking for their leadership', 'Subject: Your Leadership Needed\n\nHi {contact_name},\n\nI''d like you to take the lead on this initiative. Your strategic thinking and execution skills make you the perfect person for this. I''ll support however you need, but the decisions are yours.\n\nThanks for stepping up!', 2),

-- SANGUINE + SOCIAL CONNECTION
('sanguine', 'Social Connection', 'whatsapp', 'Fun Plans with {contact_name}', 'Send a WhatsApp about spending quality time together', 'Message them on WhatsApp about hanging out or doing something fun', 'Hey {contact_name}! 😊 I was thinking we should catch up soon - maybe grab coffee or try that new restaurant? I always have the best time talking with you! When are you free? 🎉', 1),
('sanguine', 'Social Connection', 'call', 'Spontaneous Chat with {contact_name}', 'Call them just to connect and share what''s happening', 'Make a friendly call to catch up and share stories', 'Call them and say: "I was just thinking about you and wanted to hear your voice! How''s your day going? I have to tell you about..."', 2),
('sanguine', 'Social Connection', 'activity', 'Group Activity with {contact_name}', 'Invite them to a social gathering or group activity', 'Plan a group activity or invite them to join others', 'Organize a game night, dinner party, or group outing and make sure to invite them as a key part of the fun', 3),

-- SANGUINE + APPRECIATION & PRAISE
('sanguine', 'Appreciation & Praise', 'text', 'Brighten {contact_name}''s Day', 'Send an uplifting message about their positive qualities', 'Text them specific appreciation for who they are', 'Good morning {contact_name}! ☀️ Just wanted to say you bring so much joy and energy to everyone around you. Your positivity is contagious and makes everything better! Hope you have an amazing day! 🌟', 1),
('sanguine', 'Appreciation & Praise', 'whatsapp', 'Voice Note Appreciation', 'Send a voice note expressing genuine appreciation', 'Record a warm voice message telling them what you appreciate about them', 'Send a voice note: "Hey beautiful soul! I was just thinking about how grateful I am to have you in my life. Your enthusiasm and warmth make every day brighter!"', 2),

-- MELANCHOLIC + DEEP UNDERSTANDING
('melancholic', 'Deep Understanding', 'text', 'Thoughtful Check-in with {contact_name}', 'Send a message showing you understand their deeper thoughts and feelings', 'Text them something that shows you really see and understand them', 'Hi {contact_name}, I''ve been thinking about our conversation about {topic}. I can tell it really matters to you, and I want you to know I see the depth of your thoughts on this. How are you processing it all? 💭', 1),
('melancholic', 'Deep Understanding', 'email', 'Meaningful Correspondence', 'Write a thoughtful email about something important to them', 'Send a detailed email showing you understand their perspective and values', 'Subject: Your Insights on {Topic}\n\nDear {contact_name},\n\nI''ve been reflecting on your thoughts about {topic}. Your perspective always adds such depth and meaning to these conversations. I really appreciate how thoughtfully you approach complex issues...\n\nWith appreciation for your wisdom', 2),

-- MELANCHOLIC + CREATIVE EXPRESSION
('melancholic', 'Creative Expression', 'text', 'Celebrate {contact_name}''s Creativity', 'Message them about their creative work or artistic nature', 'Text appreciating their creative talents and encouraging their artistic expression', 'Hi {contact_name}, I saw your latest {creative work} and it moved me deeply. You have such a gift for capturing beauty and meaning. Please keep creating - the world needs your unique perspective! 🎨✨', 1),
('melancholic', 'Creative Expression', 'gift', 'Creative Gift for {contact_name}', 'Give them something that supports their creative expression', 'Buy or make something that honors their artistic side', 'Get them art supplies, a beautiful journal, tickets to a museum, or something handmade that shows you value their creativity', 2),

-- PHLEGMATIC + PEACE & HARMONY
('phlegmatic', 'Peace & Harmony', 'text', 'Peaceful Message for {contact_name}', 'Send a calming, supportive message', 'Text them something gentle and peaceful', 'Hi {contact_name} 🕊️ Just wanted to send you some peaceful vibes today. I hope you''re finding moments of calm and that everything feels balanced for you. You bring such serenity to my life. 💚', 1),
('phlegmatic', 'Peace & Harmony', 'call', 'Gentle Check-in Call', 'Make a soft, supportive phone call', 'Call them with a calm, caring energy to see how they''re doing', 'Call with a gentle tone: "Hi sweetheart, I just wanted to hear your voice and see how you''re doing. No pressure to talk long - just wanted you to know I''m thinking of you."', 2),

-- PHLEGMATIC + GENTLE SUPPORT
('phlegmatic', 'Gentle Support', 'text', 'Quiet Encouragement', 'Send a supportive message without pressure', 'Text them gentle encouragement and support', 'Hey {contact_name}, I know things have been a lot lately. Just wanted you to know I''m here if you need anything at all - even just someone to sit quietly with. You''re doing great. 🤗', 1),
('phlegmatic', 'Gentle Support', 'in_person', 'Comfortable Presence', 'Spend quiet, comfortable time together', 'Offer to just be present with them in a low-key way', 'Suggest: "Would you like some company? We could just sit together, watch a movie, or I could help with something around the house. No pressure to talk or do anything special."', 2),

-- UNIVERSAL TEMPLATES
('universal', 'Physical Affection', 'in_person', 'Physical Connection with {contact_name}', 'Give them appropriate physical affection', 'Offer a hug, hold their hand, or give appropriate physical comfort', 'When you see them, give a warm hug and say: "I''ve missed you! It''s so good to see you." Physical touch can be healing and connecting.', 1),
('universal', 'Acts of Service', 'activity', 'Help {contact_name} with Something', 'Do something helpful to make their life easier', 'Identify something they need help with and offer to do it', 'Text: "I know you''ve been swamped. Can I pick up groceries, walk your dog, or help with {specific task}? I''d love to make your day a little easier!" Then follow through.', 1),
('universal', 'Thoughtful Gifts', 'gift', 'Meaningful Gift for {contact_name}', 'Give them something thoughtful and personal', 'Choose a gift that shows you know and care about them specifically', 'Think about their interests, needs, or something they mentioned wanting. It doesn''t have to be expensive - just thoughtful and personal.', 1);

-- Create function to get personalized circle reminders
CREATE OR REPLACE FUNCTION get_personalized_circle_reminders(user_uuid UUID)
RETURNS TABLE (
  id UUID,
  contact_id UUID,
  contact_name TEXT,
  contact_temperament TEXT,
  love_tank_category TEXT,
  love_tank_level INTEGER,
  action_type TEXT,
  title TEXT,
  message TEXT,
  action_suggestion TEXT,
  sample_message TEXT,
  priority INTEGER
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the requesting user matches the user_uuid
  IF auth.uid() != user_uuid THEN
    RAISE EXCEPTION 'Access denied';
  END IF;

  RETURN QUERY
  SELECT 
    gen_random_uuid() as id,
    cc.id as contact_id,
    cc.name as contact_name,
    COALESCE(cc.dominant_temperament, 'universal') as contact_temperament,
    ltc.name as love_tank_category,
    COALESCE(clt.current_level, 50) as love_tank_level,
    rt.action_type,
    REPLACE(rt.title_template, '{contact_name}', cc.name) as title,
    REPLACE(rt.message_template, '{contact_name}', cc.name) as message,
    REPLACE(rt.action_suggestion, '{contact_name}', cc.name) as action_suggestion,
    REPLACE(REPLACE(rt.sample_message, '{contact_name}', cc.name), '{project/situation}', 'current project') as sample_message,
    rt.priority
  FROM circle_contacts cc
  CROSS JOIN love_tank_categories ltc
  LEFT JOIN circle_love_tanks clt ON (cc.id = clt.contact_id AND ltc.id = clt.category_id)
  LEFT JOIN reminder_templates rt ON (
    (COALESCE(cc.dominant_temperament, 'universal') = rt.contact_temperament OR rt.contact_temperament = 'universal')
    AND ltc.name = rt.love_tank_category
  )
  WHERE cc.user_id = user_uuid
    AND rt.id IS NOT NULL
    AND COALESCE(clt.current_level, 50) < 80 -- Only show reminders for love tanks that need filling
    AND NOT EXISTS (
      -- Don't show if action was taken recently
      SELECT 1 FROM reminder_actions ra 
      WHERE ra.contact_id = cc.id 
        AND ra.love_tank_category_id = ltc.id 
        AND ra.action_type = rt.action_type
        AND ra.completed_at > NOW() - INTERVAL '1 week'
    )
  ORDER BY 
    COALESCE(clt.current_level, 50) ASC, -- Prioritize lower love tank levels
    rt.priority ASC,
    RANDOM()
  LIMIT 5;
END;
$$;

-- Create function to record completed reminder action
CREATE OR REPLACE FUNCTION record_reminder_action(
  contact_uuid UUID,
  love_tank_category_name TEXT,
  action_type_param TEXT,
  action_taken_param TEXT DEFAULT NULL,
  message_sent_param TEXT DEFAULT NULL,
  love_tank_increase_param INTEGER DEFAULT 10
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  category_id UUID;
BEGIN
  -- Get the love tank category ID
  SELECT id INTO category_id 
  FROM love_tank_categories 
  WHERE name = love_tank_category_name;

  -- Record the action
  INSERT INTO reminder_actions (
    user_id,
    contact_id,
    love_tank_category_id,
    action_type,
    action_taken,
    message_sent,
    love_tank_increase
  ) VALUES (
    auth.uid(),
    contact_uuid,
    category_id,
    action_type_param,
    action_taken_param,
    message_sent_param,
    love_tank_increase_param
  );

  -- Update or create the love tank level
  INSERT INTO circle_love_tanks (
    user_id,
    contact_id,
    category_id,
    current_level,
    last_filled_at,
    updated_at
  ) VALUES (
    auth.uid(),
    contact_uuid,
    category_id,
    LEAST(100, 50 + love_tank_increase_param),
    NOW(),
    NOW()
  )
  ON CONFLICT (contact_id, category_id)
  DO UPDATE SET
    current_level = LEAST(100, circle_love_tanks.current_level + love_tank_increase_param),
    last_filled_at = NOW(),
    updated_at = NOW();
END;
$$;

-- Create function to get love tank status for a contact
CREATE OR REPLACE FUNCTION get_contact_love_tanks(contact_uuid UUID)
RETURNS TABLE (
  category_name TEXT,
  category_icon TEXT,
  current_level INTEGER,
  target_level INTEGER,
  last_filled_at TIMESTAMPTZ,
  days_since_filled INTEGER
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ltc.name as category_name,
    ltc.icon as category_icon,
    COALESCE(clt.current_level, 50) as current_level,
    COALESCE(clt.target_level, 80) as target_level,
    clt.last_filled_at,
    CASE 
      WHEN clt.last_filled_at IS NOT NULL 
      THEN EXTRACT(days FROM NOW() - clt.last_filled_at)::INTEGER
      ELSE NULL 
    END as days_since_filled
  FROM love_tank_categories ltc
  LEFT JOIN circle_love_tanks clt ON (ltc.id = clt.category_id AND clt.contact_id = contact_uuid)
  LEFT JOIN circle_contacts cc ON (clt.contact_id = cc.id OR cc.id = contact_uuid)
  WHERE cc.user_id = auth.uid()
    AND (
      ltc.temperament = COALESCE(cc.dominant_temperament, 'universal') 
      OR ltc.temperament = 'universal'
    )
  ORDER BY ltc.priority;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_personalized_circle_reminders(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION record_reminder_action(UUID, TEXT, TEXT, TEXT, TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_contact_love_tanks(UUID) TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS circle_love_tanks_contact_category_idx ON circle_love_tanks(contact_id, category_id);
CREATE INDEX IF NOT EXISTS circle_love_tanks_user_id_idx ON circle_love_tanks(user_id);
CREATE INDEX IF NOT EXISTS reminder_templates_temperament_category_idx ON reminder_templates(contact_temperament, love_tank_category);
CREATE INDEX IF NOT EXISTS reminder_actions_contact_category_idx ON reminder_actions(contact_id, love_tank_category_id);
CREATE INDEX IF NOT EXISTS reminder_actions_completed_at_idx ON reminder_actions(completed_at);