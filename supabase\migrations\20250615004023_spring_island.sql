/*
  # Create get_active_reminders RPC function

  1. Functions
    - `get_active_reminders(user_uuid)` - Returns active reminders for a user
    - `mark_reminder_shown(reminder_id, reminder_type)` - Marks a reminder as shown
    - `update_love_tank_level(tank_id, new_level)` - Updates love tank level

  2. Security
    - Functions are accessible to authenticated users only
    - Row-level security is enforced within functions
*/

-- Create get_active_reminders function
CREATE OR REPLACE FUNCTION get_active_reminders(user_uuid uuid)
RETURNS TABLE (
  id uuid,
  title text,
  message text,
  category text,
  temperament text,
  frequency text,
  reminder_type text,
  contact_name text,
  last_shown_at timestamptz,
  created_at timestamptz
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if user is authenticated
  IF auth.uid() IS NULL OR auth.uid() != user_uuid THEN
    RAISE EXCEPTION 'Access denied';
  END IF;

  RETURN QUERY
  -- Get personal reminders
  SELECT 
    pr.id,
    pr.title,
    pr.message,
    pr.category,
    pr.temperament,
    pr.frequency,
    'personal'::text as reminder_type,
    NULL::text as contact_name,
    pr.last_shown_at,
    pr.created_at
  FROM personal_reminders pr
  WHERE pr.user_id = user_uuid 
    AND pr.is_active = true
    AND (
      pr.last_shown_at IS NULL 
      OR (
        pr.frequency = 'daily' AND pr.last_shown_at < NOW() - INTERVAL '1 day'
      )
      OR (
        pr.frequency = 'weekly' AND pr.last_shown_at < NOW() - INTERVAL '1 week'
      )
      OR (
        pr.frequency = 'monthly' AND pr.last_shown_at < NOW() - INTERVAL '1 month'
      )
    )
  
  UNION ALL
  
  -- Get circle reminders
  SELECT 
    cr.id,
    cr.title,
    cr.message,
    cr.category,
    cr.temperament,
    cr.frequency,
    'circle'::text as reminder_type,
    cc.name as contact_name,
    cr.last_shown_at,
    cr.created_at
  FROM circle_reminders cr
  LEFT JOIN circle_contacts cc ON cr.contact_id = cc.id
  WHERE cr.user_id = user_uuid 
    AND cr.is_active = true
    AND (
      cr.last_shown_at IS NULL 
      OR (
        cr.frequency = 'daily' AND cr.last_shown_at < NOW() - INTERVAL '1 day'
      )
      OR (
        cr.frequency = 'weekly' AND cr.last_shown_at < NOW() - INTERVAL '1 week'
      )
      OR (
        cr.frequency = 'monthly' AND cr.last_shown_at < NOW() - INTERVAL '1 month'
      )
    )
  
  ORDER BY created_at DESC
  LIMIT 10;
END;
$$;

-- Create mark_reminder_shown function
CREATE OR REPLACE FUNCTION mark_reminder_shown(reminder_id uuid, reminder_type text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if user is authenticated
  IF auth.uid() IS NULL THEN
    RAISE EXCEPTION 'Access denied';
  END IF;

  IF reminder_type = 'personal' THEN
    UPDATE personal_reminders 
    SET last_shown_at = NOW()
    WHERE id = reminder_id AND user_id = auth.uid();
  ELSIF reminder_type = 'circle' THEN
    UPDATE circle_reminders 
    SET last_shown_at = NOW()
    WHERE id = reminder_id AND user_id = auth.uid();
  ELSE
    RAISE EXCEPTION 'Invalid reminder type';
  END IF;
END;
$$;

-- Create update_love_tank_level function
CREATE OR REPLACE FUNCTION update_love_tank_level(tank_id uuid, new_level integer)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if user is authenticated
  IF auth.uid() IS NULL THEN
    RAISE EXCEPTION 'Access denied';
  END IF;

  -- Validate level range
  IF new_level < 0 OR new_level > 100 THEN
    RAISE EXCEPTION 'Level must be between 0 and 100';
  END IF;

  UPDATE love_tanks 
  SET 
    current_level = new_level,
    last_filled_at = NOW(),
    updated_at = NOW()
  WHERE id = tank_id AND user_id = auth.uid();
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_active_reminders(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION mark_reminder_shown(uuid, text) TO authenticated;
GRANT EXECUTE ON FUNCTION update_love_tank_level(uuid, integer) TO authenticated;