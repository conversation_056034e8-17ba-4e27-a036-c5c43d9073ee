import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Switch,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { supabase } from '@/lib/supabase';
import { SavedInsight } from '@/types/personality';
import { UserProfile } from '@/types/database';
import {
  LogOut,
  User,
  Mail,
  Shield,
  Bell,
  Moon,
  CircleHelp as HelpCircle,
  MessageSquare,
  Star,
  ChevronRight,
  Settings as SettingsIcon,
  Trash2,
  Bookmark,
  X,
  Lightbulb,
  Quote,
  RefreshCw,
} from 'lucide-react-native';

export default function SettingsScreen() {
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [loggingOut, setLoggingOut] = useState(false);
  const [retakingAssessment, setRetakingAssessment] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [darkModeEnabled, setDarkModeEnabled] = useState(false);
  const [savedInsights, setSavedInsights] = useState<SavedInsight[]>([]);
  const [showSavedInsights, setShowSavedInsights] = useState(false);
  const [loadingSavedInsights, setLoadingSavedInsights] = useState(false);

  useEffect(() => {
    loadUserProfile();
  }, []);

  const loadUserProfile = async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) return;

      const { data: profile } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profile) {
        setUserProfile(profile);
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSavedInsights = async () => {
    setLoadingSavedInsights(true);
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) return;

      const { data: insights, error } = await supabase
        .from('saved_insights')
        .select('*')
        .eq('user_id', user.id)
        .order('saved_at', { ascending: false });

      if (error) throw error;

      setSavedInsights(insights || []);
    } catch (error) {
      console.error('Error loading saved insights:', error);
      Alert.alert('Error', 'Failed to load saved insights');
    } finally {
      setLoadingSavedInsights(false);
    }
  };

  const handleDeleteSavedInsight = async (insightId: string) => {
    try {
      const { error } = await supabase
        .from('saved_insights')
        .delete()
        .eq('id', insightId);

      if (error) throw error;

      setSavedInsights((prev) =>
        prev.filter((insight) => insight.id !== insightId)
      );
      Alert.alert('Deleted', 'Insight removed from saved items');
    } catch (error) {
      console.error('Error deleting saved insight:', error);
      Alert.alert('Error', 'Failed to delete insight');
    }
  };

  const handleShowSavedInsights = () => {
    setShowSavedInsights(true);
    loadSavedInsights();
  };

  const handleRetakeAssessment = () => {
    Alert.alert(
      'Retake Personality Assessment',
      'This will replace your current personality profile with a new one based on fresh assessment questions. Your previous results will be permanently overwritten.\n\nAre you sure you want to continue?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Retake Assessment',
          style: 'destructive',
          onPress: async () => {
            setRetakingAssessment(true);
            try {
              const {
                data: { user },
              } = await supabase.auth.getUser();
              if (!user) {
                Alert.alert('Error', 'Please sign in to retake the assessment');
                return;
              }

              console.log('🔄 Starting assessment retake process...');

              // Step 1: Delete existing personality profiles
              console.log('🗑️ Deleting old personality profiles...');
              const { error: profileError } = await supabase
                .from('personality_profiles')
                .delete()
                .eq('user_id', user.id);

              if (profileError) {
                console.warn(
                  'Warning: Could not delete old personality profiles:',
                  profileError
                );
                // Continue anyway - this might not be critical
              } else {
                console.log('✅ Old personality profiles deleted');
              }

              // Step 2: Delete existing assessment responses
              console.log('🗑️ Deleting old assessment responses...');
              const { error: responsesError } = await supabase
                .from('assessment_responses')
                .delete()
                .eq('user_id', user.id);

              if (responsesError) {
                console.warn(
                  'Warning: Could not delete old assessment responses:',
                  responsesError
                );
                // Continue anyway - this might not be critical
              } else {
                console.log('✅ Old assessment responses deleted');
              }

              // Step 3: Update user profile to mark assessment as incomplete
              console.log('📝 Updating user profile...');
              const { error: userError } = await supabase
                .from('user_profiles')
                .update({ has_completed_assessment: false })
                .eq('id', user.id);

              if (userError) {
                console.warn(
                  'Warning: Could not update user profile:',
                  userError
                );
                // Continue anyway - the assessment screen will handle this
              } else {
                console.log('✅ User profile updated');
              }

              console.log('🎉 Assessment data cleaned up successfully');

              // Step 4: Navigate to assessment
              console.log('🚀 Navigating to assessment...');
              router.push('/assessment');
            } catch (error: any) {
              console.error('💥 Error preparing assessment retake:', error);
              Alert.alert(
                'Error',
                `Failed to prepare assessment retake: ${
                  error.message || 'Unknown error'
                }. Please try again or contact support if the problem persists.`
              );
            } finally {
              setRetakingAssessment(false);
            }
          },
        },
      ]
    );
  };

  const handleLogout = async () => {
    Alert.alert('Sign Out', 'Are you sure you want to sign out?', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Sign Out',
        style: 'destructive',
        onPress: async () => {
          setLoggingOut(true);
          try {
            const { error } = await supabase.auth.signOut();
            if (error) throw error;

            // Clear any local state if needed
            setUserProfile(null);

            // Navigate to auth screen
            router.replace('/auth');
          } catch (error: any) {
            console.error('Error signing out:', error);
            Alert.alert('Error', 'Failed to sign out. Please try again.');
          } finally {
            setLoggingOut(false);
          }
        },
      },
    ]);
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'This action cannot be undone. All your data will be permanently deleted.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Coming Soon',
              'Account deletion feature will be available in a future update. For now, please contact support if you need to delete your account.'
            );
          },
        },
      ]
    );
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'insight':
        return <Lightbulb size={16} color="#F59E0B" />;
      case 'tip':
        return <MessageSquare size={16} color="#10B981" />;
      case 'quote':
        return <Quote size={16} color="#8B5CF6" />;
      default:
        return <Bookmark size={16} color="#6B7280" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const SettingItem = ({
    icon,
    title,
    subtitle,
    onPress,
    showChevron = true,
    rightElement,
    disabled = false,
  }: {
    icon: React.ReactNode;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    showChevron?: boolean;
    rightElement?: React.ReactNode;
    disabled?: boolean;
  }) => (
    <TouchableOpacity
      style={[styles.settingItem, disabled && styles.settingItemDisabled]}
      onPress={onPress}
      disabled={!onPress || disabled}
    >
      <View style={styles.settingLeft}>
        <View
          style={[
            styles.iconContainer,
            disabled && styles.iconContainerDisabled,
          ]}
        >
          {icon}
        </View>
        <View style={styles.settingText}>
          <Text
            style={[
              styles.settingTitle,
              disabled && styles.settingTitleDisabled,
            ]}
          >
            {title}
          </Text>
          {subtitle && (
            <Text
              style={[
                styles.settingSubtitle,
                disabled && styles.settingSubtitleDisabled,
              ]}
            >
              {subtitle}
            </Text>
          )}
        </View>
      </View>
      <View style={styles.settingRight}>
        {rightElement}
        {showChevron && onPress && !disabled && (
          <ChevronRight size={20} color="#9CA3AF" />
        )}
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading settings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <SettingsIcon size={32} color="#3B82F6" />
          <Text style={styles.headerTitle}>Settings</Text>
          <Text style={styles.headerSubtitle}>
            Manage your account and preferences
          </Text>
        </View>

        {/* Profile Section */}
        {userProfile && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Profile</Text>
            <View style={styles.profileCard}>
              <View style={styles.profileInfo}>
                <View style={styles.profileAvatar}>
                  <User size={24} color="#3B82F6" />
                </View>
                <View style={styles.profileText}>
                  <Text style={styles.profileName}>
                    {userProfile.full_name || 'User'}
                  </Text>
                  <Text style={styles.profileEmail}>{userProfile.email}</Text>
                  <View style={styles.verificationBadge}>
                    <Shield
                      size={12}
                      color={userProfile.email_verified ? '#10B981' : '#F59E0B'}
                    />
                    <Text
                      style={[
                        styles.verificationText,
                        {
                          color: userProfile.email_verified
                            ? '#10B981'
                            : '#F59E0B',
                        },
                      ]}
                    >
                      {userProfile.email_verified ? 'Verified' : 'Unverified'}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* Account Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          <View style={styles.settingsGroup}>
            <SettingItem
              icon={<User size={20} color="#6B7280" />}
              title="Edit Profile"
              subtitle="Update your personal information"
              onPress={() =>
                Alert.alert(
                  'Coming Soon',
                  'Profile editing will be available in a future update.'
                )
              }
            />
            <SettingItem
              icon={<Mail size={20} color="#6B7280" />}
              title="Email Settings"
              subtitle="Manage email preferences"
              onPress={() =>
                Alert.alert(
                  'Coming Soon',
                  'Email settings will be available in a future update.'
                )
              }
            />
            <SettingItem
              icon={<Shield size={20} color="#6B7280" />}
              title="Privacy & Security"
              subtitle="Control your privacy settings"
              onPress={() =>
                Alert.alert(
                  'Coming Soon',
                  'Privacy settings will be available in a future update.'
                )
              }
            />
          </View>
        </View>

        {/* Personality Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Personality</Text>
          <View style={styles.settingsGroup}>
            <SettingItem
              icon={
                <RefreshCw
                  size={20}
                  color={retakingAssessment ? '#9CA3AF' : '#6B7280'}
                />
              }
              title="Retake Assessment"
              subtitle={
                retakingAssessment
                  ? 'Preparing assessment...'
                  : 'Update your personality profile'
              }
              onPress={retakingAssessment ? undefined : handleRetakeAssessment}
              disabled={retakingAssessment}
              rightElement={
                retakingAssessment ? (
                  <Text style={styles.loadingText}>Processing...</Text>
                ) : null
              }
            />
            <SettingItem
              icon={<Bookmark size={20} color="#6B7280" />}
              title="Saved"
              subtitle="View your saved insights, tips, and quotes"
              onPress={handleShowSavedInsights}
            />
          </View>
        </View>

        {/* Preferences Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferences</Text>
          <View style={styles.settingsGroup}>
            <SettingItem
              icon={<Bell size={20} color="#6B7280" />}
              title="Notifications"
              subtitle="Daily insights and reminders"
              showChevron={false}
              rightElement={
                <Switch
                  value={notificationsEnabled}
                  onValueChange={setNotificationsEnabled}
                  trackColor={{ false: '#E5E7EB', true: '#3B82F6' }}
                  thumbColor="#ffffff"
                />
              }
            />
            <SettingItem
              icon={<Moon size={20} color="#6B7280" />}
              title="Dark Mode"
              subtitle="Switch to dark theme"
              showChevron={false}
              rightElement={
                <Switch
                  value={darkModeEnabled}
                  onValueChange={setDarkModeEnabled}
                  trackColor={{ false: '#E5E7EB', true: '#3B82F6' }}
                  thumbColor="#ffffff"
                />
              }
            />
          </View>
        </View>

        {/* Support Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          <View style={styles.settingsGroup}>
            <SettingItem
              icon={<HelpCircle size={20} color="#6B7280" />}
              title="Help & FAQ"
              subtitle="Get answers to common questions"
              onPress={() =>
                Alert.alert(
                  'Coming Soon',
                  'Help section will be available in a future update.'
                )
              }
            />
            <SettingItem
              icon={<MessageSquare size={20} color="#6B7280" />}
              title="Contact Support"
              subtitle="Get help from our team"
              onPress={() =>
                Alert.alert(
                  'Coming Soon',
                  'Support contact will be available in a future update.'
                )
              }
            />
          </View>
        </View>

        {/* Danger Zone */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Danger Zone</Text>
          <View style={styles.settingsGroup}>
            <SettingItem
              icon={<LogOut size={20} color="#EF4444" />}
              title="Sign Out"
              subtitle="Sign out of your account"
              onPress={handleLogout}
              showChevron={false}
              rightElement={
                loggingOut ? (
                  <Text style={styles.loadingText}>Signing out...</Text>
                ) : null
              }
            />
            <SettingItem
              icon={<Trash2 size={20} color="#EF4444" />}
              title="Delete Account"
              subtitle="Permanently delete your account"
              onPress={handleDeleteAccount}
              showChevron={false}
            />
          </View>
        </View>

        {/* App Info */}
        <View style={styles.appInfo}>
          <Text style={styles.appInfoText}>Harmona v1.0.0</Text>
          <Text style={styles.appInfoText}>
            Made with ❤️ for better relationships
          </Text>
        </View>
      </ScrollView>

      {/* Saved Insights Modal */}
      <Modal
        visible={showSavedInsights}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowSavedInsights(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Saved Insights</Text>
              <TouchableOpacity onPress={() => setShowSavedInsights(false)}>
                <X size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <ScrollView
              style={styles.savedInsightsList}
              showsVerticalScrollIndicator={false}
            >
              {loadingSavedInsights ? (
                <View style={styles.loadingContainer}>
                  <Text style={styles.loadingText}>
                    Loading saved insights...
                  </Text>
                </View>
              ) : savedInsights.length === 0 ? (
                <View style={styles.emptyState}>
                  <Bookmark size={48} color="#D1D5DB" />
                  <Text style={styles.emptyTitle}>No saved insights yet</Text>
                  <Text style={styles.emptyText}>
                    Save insights, tips, and quotes from your daily dose to view
                    them here
                  </Text>
                </View>
              ) : (
                savedInsights.map((insight) => (
                  <View key={insight.id} style={styles.savedInsightItem}>
                    <View style={styles.savedInsightHeader}>
                      <View style={styles.savedInsightType}>
                        {getInsightIcon(insight.insight_type)}
                        <Text style={styles.savedInsightTypeText}>
                          {insight.insight_type.charAt(0).toUpperCase() +
                            insight.insight_type.slice(1)}
                        </Text>
                      </View>
                      <TouchableOpacity
                        style={styles.deleteButton}
                        onPress={() => {
                          Alert.alert(
                            'Delete Insight',
                            'Are you sure you want to remove this from your saved insights?',
                            [
                              { text: 'Cancel', style: 'cancel' },
                              {
                                text: 'Delete',
                                style: 'destructive',
                                onPress: () =>
                                  handleDeleteSavedInsight(insight.id),
                              },
                            ]
                          );
                        }}
                      >
                        <Trash2 size={16} color="#EF4444" />
                      </TouchableOpacity>
                    </View>
                    <Text style={styles.savedInsightContent}>
                      {insight.content}
                    </Text>
                    <View style={styles.savedInsightFooter}>
                      <Text style={styles.savedInsightTemperament}>
                        {insight.temperament.charAt(0).toUpperCase() +
                          insight.temperament.slice(1)}
                      </Text>
                      <Text style={styles.savedInsightDate}>
                        {formatDate(insight.saved_at)}
                      </Text>
                    </View>
                  </View>
                ))
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 32,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1F2937',
    marginTop: 12,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
    paddingHorizontal: 24,
  },
  profileCard: {
    backgroundColor: '#ffffff',
    marginHorizontal: 24,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#EBF8FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  profileText: {
    flex: 1,
  },
  profileName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  verificationText: {
    fontSize: 12,
    fontWeight: '500',
  },
  settingsGroup: {
    backgroundColor: '#ffffff',
    marginHorizontal: 24,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  settingItemDisabled: {
    opacity: 0.6,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  iconContainerDisabled: {
    backgroundColor: '#F9FAFB',
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 2,
  },
  settingTitleDisabled: {
    color: '#9CA3AF',
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#6B7280',
  },
  settingSubtitleDisabled: {
    color: '#D1D5DB',
  },
  settingRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  appInfo: {
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 32,
    gap: 4,
  },
  appInfoText: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 40,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
  },
  savedInsightsList: {
    maxHeight: 500,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
    gap: 12,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B7280',
  },
  emptyText: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 20,
  },
  savedInsightItem: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  savedInsightHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  savedInsightType: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  savedInsightTypeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
  },
  deleteButton: {
    padding: 4,
  },
  savedInsightContent: {
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
    marginBottom: 12,
  },
  savedInsightFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  savedInsightTemperament: {
    fontSize: 12,
    color: '#3B82F6',
    fontWeight: '500',
    backgroundColor: '#EBF8FF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  savedInsightDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
});
