/*
  # Fix missing database function and foreign key relationship

  1. Database Functions
    - Create `get_active_reminders` function to fetch active reminders for users
    - Function combines personal and circle reminders based on frequency and last shown time

  2. Foreign Key Fixes
    - Ensure proper foreign key relationship between love_tanks and love_tank_categories tables
    - Add any missing constraints that may have been dropped

  3. Security
    - Maintain existing RLS policies
    - Ensure function respects user isolation
*/

-- Create the missing get_active_reminders function
CREATE OR REPLACE FUNCTION public.get_active_reminders(user_uuid uuid)
RETURNS TABLE (
  id uuid,
  title text,
  message text,
  category text,
  temperament text,
  frequency text,
  reminder_type text,
  contact_name text,
  last_shown_at timestamptz,
  created_at timestamptz
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  -- Get personal reminders
  SELECT 
    pr.id,
    pr.title,
    pr.message,
    pr.category,
    pr.temperament,
    pr.frequency,
    'personal'::text as reminder_type,
    NULL::text as contact_name,
    pr.last_shown_at,
    pr.created_at
  FROM personal_reminders pr
  WHERE pr.user_id = user_uuid
    AND pr.is_active = true
    AND (
      pr.last_shown_at IS NULL OR
      (pr.frequency = 'daily' AND pr.last_shown_at < NOW() - INTERVAL '1 day') OR
      (pr.frequency = 'weekly' AND pr.last_shown_at < NOW() - INTERVAL '1 week') OR
      (pr.frequency = 'monthly' AND pr.last_shown_at < NOW() - INTERVAL '1 month')
    )
  
  UNION ALL
  
  -- Get circle reminders
  SELECT 
    cr.id,
    cr.title,
    cr.message,
    cr.category,
    cr.temperament,
    cr.frequency,
    'circle'::text as reminder_type,
    cc.name as contact_name,
    cr.last_shown_at,
    cr.created_at
  FROM circle_reminders cr
  LEFT JOIN circle_contacts cc ON cr.contact_id = cc.id
  WHERE cr.user_id = user_uuid
    AND cr.is_active = true
    AND (
      cr.last_shown_at IS NULL OR
      (cr.frequency = 'daily' AND cr.last_shown_at < NOW() - INTERVAL '1 day') OR
      (cr.frequency = 'weekly' AND cr.last_shown_at < NOW() - INTERVAL '1 week') OR
      (cr.frequency = 'monthly' AND cr.last_shown_at < NOW() - INTERVAL '1 month')
    )
  
  ORDER BY created_at DESC
  LIMIT 10;
END;
$$;

-- Ensure the foreign key relationship exists between love_tanks and love_tank_categories
-- First, check if the constraint exists and drop it if it does to recreate it properly
DO $$
BEGIN
  -- Drop existing constraint if it exists but is broken
  IF EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'love_tanks_category_id_fkey' 
    AND table_name = 'love_tanks'
  ) THEN
    ALTER TABLE love_tanks DROP CONSTRAINT love_tanks_category_id_fkey;
  END IF;
END $$;

-- Recreate the foreign key constraint
ALTER TABLE love_tanks 
ADD CONSTRAINT love_tanks_category_id_fkey 
FOREIGN KEY (category_id) REFERENCES love_tank_categories(id) ON DELETE CASCADE;

-- Grant execute permission on the function to authenticated users
GRANT EXECUTE ON FUNCTION public.get_active_reminders(uuid) TO authenticated;