-- Drop existing functions to avoid conflicts
DROP FUNCTION IF EXISTS get_active_reminders(UUID);
DROP FUNCTION IF EXISTS mark_reminder_shown(UUID, TEXT);
DROP FUNCTION IF EXISTS update_love_tank_level(UUID, INTEGER);
DROP FUNCTION IF EXISTS get_personalized_circle_reminders(UUID);
DROP FUNCTION IF EXISTS get_contact_love_tanks(UUID);
DROP FUNCTION IF EXISTS record_reminder_action(UUID, TEXT, TEXT, TEXT, INTEGER);

-- Ensure all required tables exist with proper structure
CREATE TABLE IF NOT EXISTS love_tank_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  temperament TEXT NOT NULL CHECK (temperament = ANY (ARRAY['choleric'::text, 'sanguine'::text, 'melancholic'::text, 'phlegmatic'::text, 'universal'::text])),
  icon TEXT DEFAULT '❤️',
  priority INTEGER DEFAULT 1 CHECK (priority >= 1 AND priority <= 10),
  created_at TIMESTAMPTZ DEFAULT now()
);

CREATE TABLE IF NOT EXISTS love_tanks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  category_id UUID NOT NULL,
  current_level INTEGER DEFAULT 50 CHECK (current_level >= 0 AND current_level <= 100),
  target_level INTEGER DEFAULT 80 CHECK (target_level >= 0 AND target_level <= 100),
  last_filled_at TIMESTAMPTZ,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

CREATE TABLE IF NOT EXISTS circle_love_tanks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  contact_id UUID NOT NULL,
  category_id UUID NOT NULL,
  current_level INTEGER DEFAULT 50 CHECK (current_level >= 0 AND current_level <= 100),
  target_level INTEGER DEFAULT 80 CHECK (target_level >= 0 AND target_level <= 100),
  last_filled_at TIMESTAMPTZ,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

CREATE TABLE IF NOT EXISTS reminder_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  contact_temperament TEXT NOT NULL CHECK (contact_temperament = ANY (ARRAY['choleric'::text, 'sanguine'::text, 'melancholic'::text, 'phlegmatic'::text, 'universal'::text])),
  love_tank_category TEXT NOT NULL,
  action_type TEXT NOT NULL CHECK (action_type = ANY (ARRAY['text'::text, 'whatsapp'::text, 'email'::text, 'call'::text, 'in_person'::text, 'gift'::text, 'activity'::text])),
  title_template TEXT NOT NULL,
  message_template TEXT NOT NULL,
  action_suggestion TEXT NOT NULL,
  sample_message TEXT,
  priority INTEGER DEFAULT 1,
  created_at TIMESTAMPTZ DEFAULT now()
);

CREATE TABLE IF NOT EXISTS reminder_actions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  contact_id UUID NOT NULL,
  love_tank_category_id UUID NOT NULL,
  action_type TEXT NOT NULL,
  action_taken TEXT,
  message_sent TEXT,
  completed_at TIMESTAMPTZ DEFAULT now(),
  love_tank_increase INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Add unique constraints if they don't exist
DO $$
BEGIN
  -- Add unique constraint on love_tank_categories.name
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'love_tank_categories_name_key'
    AND table_name = 'love_tank_categories'
  ) THEN
    ALTER TABLE love_tank_categories ADD CONSTRAINT love_tank_categories_name_key UNIQUE (name);
  END IF;

  -- Add unique constraint on circle_love_tanks
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'circle_love_tanks_contact_id_category_id_key'
    AND table_name = 'circle_love_tanks'
  ) THEN
    ALTER TABLE circle_love_tanks ADD CONSTRAINT circle_love_tanks_contact_id_category_id_key UNIQUE (contact_id, category_id);
  END IF;

  -- Add unique constraint on reminder_templates
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'reminder_templates_unique_key'
    AND table_name = 'reminder_templates'
  ) THEN
    ALTER TABLE reminder_templates ADD CONSTRAINT reminder_templates_unique_key UNIQUE (contact_temperament, love_tank_category, action_type);
  END IF;
END $$;

-- Add foreign key constraints if they don't exist
DO $$
BEGIN
  -- Love tanks foreign keys
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'love_tanks_category_id_fkey'
    AND table_name = 'love_tanks'
  ) THEN
    ALTER TABLE love_tanks 
    ADD CONSTRAINT love_tanks_category_id_fkey 
    FOREIGN KEY (category_id) REFERENCES love_tank_categories(id) ON DELETE CASCADE;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'love_tanks_user_id_fkey'
    AND table_name = 'love_tanks'
  ) THEN
    ALTER TABLE love_tanks 
    ADD CONSTRAINT love_tanks_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES user_profiles(id) ON DELETE CASCADE;
  END IF;

  -- Circle love tanks foreign keys
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'circle_love_tanks_user_id_fkey'
    AND table_name = 'circle_love_tanks'
  ) THEN
    ALTER TABLE circle_love_tanks 
    ADD CONSTRAINT circle_love_tanks_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES user_profiles(id) ON DELETE CASCADE;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'circle_love_tanks_contact_id_fkey'
    AND table_name = 'circle_love_tanks'
  ) THEN
    ALTER TABLE circle_love_tanks 
    ADD CONSTRAINT circle_love_tanks_contact_id_fkey 
    FOREIGN KEY (contact_id) REFERENCES circle_contacts(id) ON DELETE CASCADE;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'circle_love_tanks_category_id_fkey'
    AND table_name = 'circle_love_tanks'
  ) THEN
    ALTER TABLE circle_love_tanks 
    ADD CONSTRAINT circle_love_tanks_category_id_fkey 
    FOREIGN KEY (category_id) REFERENCES love_tank_categories(id) ON DELETE CASCADE;
  END IF;

  -- Reminder actions foreign keys
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'reminder_actions_user_id_fkey'
    AND table_name = 'reminder_actions'
  ) THEN
    ALTER TABLE reminder_actions 
    ADD CONSTRAINT reminder_actions_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES user_profiles(id) ON DELETE CASCADE;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'reminder_actions_contact_id_fkey'
    AND table_name = 'reminder_actions'
  ) THEN
    ALTER TABLE reminder_actions 
    ADD CONSTRAINT reminder_actions_contact_id_fkey 
    FOREIGN KEY (contact_id) REFERENCES circle_contacts(id) ON DELETE CASCADE;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'reminder_actions_category_id_fkey'
    AND table_name = 'reminder_actions'
  ) THEN
    ALTER TABLE reminder_actions 
    ADD CONSTRAINT reminder_actions_category_id_fkey 
    FOREIGN KEY (love_tank_category_id) REFERENCES love_tank_categories(id) ON DELETE CASCADE;
  END IF;
END $$;

-- Enable RLS on all tables
ALTER TABLE love_tank_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE love_tanks ENABLE ROW LEVEL SECURITY;
ALTER TABLE circle_love_tanks ENABLE ROW LEVEL SECURITY;
ALTER TABLE reminder_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE reminder_actions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies with proper checks for existing policies
DO $$
BEGIN
  -- Love tank categories policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'love_tank_categories' 
    AND policyname = 'Anyone can read love tank categories'
  ) THEN
    CREATE POLICY "Anyone can read love tank categories" ON love_tank_categories FOR SELECT TO authenticated USING (true);
  END IF;

  -- Love tanks policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'love_tanks' 
    AND policyname = 'Users can manage own love tanks'
  ) THEN
    CREATE POLICY "Users can manage own love tanks" ON love_tanks FOR ALL TO authenticated USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
  END IF;

  -- Circle love tanks policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'circle_love_tanks' 
    AND policyname = 'Users can manage own circle love tanks'
  ) THEN
    CREATE POLICY "Users can manage own circle love tanks" ON circle_love_tanks FOR ALL TO authenticated USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
  END IF;

  -- Reminder templates policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'reminder_templates' 
    AND policyname = 'Anyone can read reminder templates'
  ) THEN
    CREATE POLICY "Anyone can read reminder templates" ON reminder_templates FOR SELECT TO authenticated USING (true);
  END IF;

  -- Reminder actions policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'reminder_actions' 
    AND policyname = 'Users can manage own reminder actions'
  ) THEN
    CREATE POLICY "Users can manage own reminder actions" ON reminder_actions FOR ALL TO authenticated USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
  END IF;
END $$;

-- Insert love tank categories using INSERT ... WHERE NOT EXISTS pattern
INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Words of Affirmation', 'Verbal appreciation and encouragement', 'universal', '💬', 1
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Words of Affirmation');

INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Quality Time', 'Focused attention and meaningful conversations', 'universal', '⏰', 2
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Quality Time');

INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Physical Touch', 'Appropriate physical affection and comfort', 'universal', '🤗', 3
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Physical Touch');

INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Acts of Service', 'Helpful actions that make life easier', 'universal', '🛠️', 4
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Acts of Service');

INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Gifts', 'Thoughtful presents and surprises', 'universal', '🎁', 5
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Gifts');

INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Achievement Recognition', 'Acknowledgment of accomplishments and leadership', 'choleric', '🏆', 1
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Achievement Recognition');

INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Goal Support', 'Help in achieving objectives and ambitions', 'choleric', '🎯', 2
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Goal Support');

INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Respect for Decisions', 'Trust in their judgment and choices', 'choleric', '⚖️', 3
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Respect for Decisions');

INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Social Connection', 'Fun activities and social interactions', 'sanguine', '🎉', 1
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Social Connection');

INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Enthusiasm Sharing', 'Excitement about their interests and ideas', 'sanguine', '✨', 2
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Enthusiasm Sharing');

INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Adventure Together', 'New experiences and spontaneous activities', 'sanguine', '🌟', 3
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Adventure Together');

INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Deep Understanding', 'Thoughtful conversations and emotional support', 'melancholic', '💭', 1
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Deep Understanding');

INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Creative Appreciation', 'Recognition of their artistic and analytical abilities', 'melancholic', '🎨', 2
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Creative Appreciation');

INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Quiet Presence', 'Peaceful companionship and understanding silence', 'melancholic', '🕯️', 3
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Quiet Presence');

INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Peaceful Environment', 'Harmony and stress-free interactions', 'phlegmatic', '☮️', 1
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Peaceful Environment');

INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Gentle Encouragement', 'Soft motivation and patient support', 'phlegmatic', '🌱', 2
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Gentle Encouragement');

INSERT INTO love_tank_categories (name, description, temperament, icon, priority)
SELECT 'Stability Assurance', 'Consistent presence and reliable support', 'phlegmatic', '🏠', 3
WHERE NOT EXISTS (SELECT 1 FROM love_tank_categories WHERE name = 'Stability Assurance');

-- Insert reminder templates using INSERT ... WHERE NOT EXISTS pattern
INSERT INTO reminder_templates (contact_temperament, love_tank_category, action_type, title_template, message_template, action_suggestion, sample_message, priority)
SELECT 'choleric', 'Achievement Recognition', 'whatsapp', 'Celebrate {contact_name}''s Success', 'Acknowledge their recent accomplishment and leadership', 'Send a WhatsApp message recognizing their achievement', 'Hey {contact_name}! I saw you crushed that project deadline. Your leadership and drive are incredible - I''m so proud of you! 🏆', 1
WHERE NOT EXISTS (SELECT 1 FROM reminder_templates WHERE contact_temperament = 'choleric' AND love_tank_category = 'Achievement Recognition' AND action_type = 'whatsapp');

INSERT INTO reminder_templates (contact_temperament, love_tank_category, action_type, title_template, message_template, action_suggestion, sample_message, priority)
SELECT 'choleric', 'Achievement Recognition', 'email', 'Professional Recognition for {contact_name}', 'Write a detailed email about their professional accomplishments', 'Send a professional email highlighting their achievements', 'Subject: Your Outstanding Leadership\n\nHi {contact_name},\n\nI wanted to take a moment to recognize your exceptional work on the recent project. Your decisive leadership and strategic thinking made all the difference. You have a remarkable ability to turn challenges into opportunities.\n\nBest regards', 2
WHERE NOT EXISTS (SELECT 1 FROM reminder_templates WHERE contact_temperament = 'choleric' AND love_tank_category = 'Achievement Recognition' AND action_type = 'email');

INSERT INTO reminder_templates (contact_temperament, love_tank_category, action_type, title_template, message_template, action_suggestion, sample_message, priority)
SELECT 'sanguine', 'Social Connection', 'whatsapp', 'Plan Fun Time with {contact_name}', 'Suggest a social activity or gathering', 'Send a WhatsApp message proposing a fun activity', 'Hey {contact_name}! 😊 I was thinking we should catch up soon - maybe grab coffee or try that new restaurant? I always have the best time talking with you!', 1
WHERE NOT EXISTS (SELECT 1 FROM reminder_templates WHERE contact_temperament = 'sanguine' AND love_tank_category = 'Social Connection' AND action_type = 'whatsapp');

INSERT INTO reminder_templates (contact_temperament, love_tank_category, action_type, title_template, message_template, action_suggestion, sample_message, priority)
SELECT 'sanguine', 'Social Connection', 'call', 'Spontaneous Call to {contact_name}', 'Make a cheerful phone call to connect', 'Give them a call to share excitement about something', 'Call them to share some exciting news or just to hear their voice and catch up on life', 2
WHERE NOT EXISTS (SELECT 1 FROM reminder_templates WHERE contact_temperament = 'sanguine' AND love_tank_category = 'Social Connection' AND action_type = 'call');

INSERT INTO reminder_templates (contact_temperament, love_tank_category, action_type, title_template, message_template, action_suggestion, sample_message, priority)
SELECT 'melancholic', 'Deep Understanding', 'email', 'Thoughtful Message for {contact_name}', 'Write a meaningful email showing you understand them', 'Send a thoughtful email about something important to them', 'Subject: Thinking of You\n\nDear {contact_name},\n\nI''ve been reflecting on our last conversation about [topic]. Your perspective really resonated with me, and I wanted you to know how much I value your thoughtful insights. You have such a beautiful way of seeing the deeper meaning in things.\n\nWith appreciation', 1
WHERE NOT EXISTS (SELECT 1 FROM reminder_templates WHERE contact_temperament = 'melancholic' AND love_tank_category = 'Deep Understanding' AND action_type = 'email');

INSERT INTO reminder_templates (contact_temperament, love_tank_category, action_type, title_template, message_template, action_suggestion, sample_message, priority)
SELECT 'melancholic', 'Deep Understanding', 'in_person', 'Quality Conversation with {contact_name}', 'Plan a meaningful one-on-one conversation', 'Arrange a quiet meeting to have a deep conversation', 'Invite them for a peaceful walk or quiet coffee where you can have an uninterrupted, meaningful conversation', 2
WHERE NOT EXISTS (SELECT 1 FROM reminder_templates WHERE contact_temperament = 'melancholic' AND love_tank_category = 'Deep Understanding' AND action_type = 'in_person');

INSERT INTO reminder_templates (contact_temperament, love_tank_category, action_type, title_template, message_template, action_suggestion, sample_message, priority)
SELECT 'phlegmatic', 'Peaceful Environment', 'whatsapp', 'Gentle Check-in with {contact_name}', 'Send a calm, supportive message', 'Send a peaceful WhatsApp message offering support', 'Hi {contact_name} 🌸 Just wanted to check in and see how you''re doing. No pressure to respond right away - just know I''m thinking of you and here if you need anything.', 1
WHERE NOT EXISTS (SELECT 1 FROM reminder_templates WHERE contact_temperament = 'phlegmatic' AND love_tank_category = 'Peaceful Environment' AND action_type = 'whatsapp');

INSERT INTO reminder_templates (contact_temperament, love_tank_category, action_type, title_template, message_template, action_suggestion, sample_message, priority)
SELECT 'phlegmatic', 'Gentle Encouragement', 'in_person', 'Quiet Support for {contact_name}', 'Offer gentle, in-person encouragement', 'Spend quiet time together offering gentle support', 'Suggest a calm activity like a peaceful walk, gardening together, or just sitting and talking in a comfortable environment', 2
WHERE NOT EXISTS (SELECT 1 FROM reminder_templates WHERE contact_temperament = 'phlegmatic' AND love_tank_category = 'Gentle Encouragement' AND action_type = 'in_person');

INSERT INTO reminder_templates (contact_temperament, love_tank_category, action_type, title_template, message_template, action_suggestion, sample_message, priority)
SELECT 'universal', 'Words of Affirmation', 'whatsapp', 'Encouraging Words for {contact_name}', 'Send uplifting and affirming message', 'Send a WhatsApp message with words of affirmation', 'Hey {contact_name}! I just wanted you to know how much you mean to me. You bring so much positivity to my life, and I''m grateful to have you in it! 💕', 1
WHERE NOT EXISTS (SELECT 1 FROM reminder_templates WHERE contact_temperament = 'universal' AND love_tank_category = 'Words of Affirmation' AND action_type = 'whatsapp');

INSERT INTO reminder_templates (contact_temperament, love_tank_category, action_type, title_template, message_template, action_suggestion, sample_message, priority)
SELECT 'universal', 'Quality Time', 'call', 'Dedicated Time with {contact_name}', 'Plan focused time together', 'Schedule a call or meeting for quality time', 'Set aside uninterrupted time to really connect - put away phones and focus entirely on each other', 1
WHERE NOT EXISTS (SELECT 1 FROM reminder_templates WHERE contact_temperament = 'universal' AND love_tank_category = 'Quality Time' AND action_type = 'call');

INSERT INTO reminder_templates (contact_temperament, love_tank_category, action_type, title_template, message_template, action_suggestion, sample_message, priority)
SELECT 'universal', 'Acts of Service', 'whatsapp', 'Offer Help to {contact_name}', 'Ask how you can help them today', 'Send a message offering to help with something', 'Hi {contact_name}! Is there anything I can help you with today? I''d love to make your day a little easier in some way 😊', 1
WHERE NOT EXISTS (SELECT 1 FROM reminder_templates WHERE contact_temperament = 'universal' AND love_tank_category = 'Acts of Service' AND action_type = 'whatsapp');

INSERT INTO reminder_templates (contact_temperament, love_tank_category, action_type, title_template, message_template, action_suggestion, sample_message, priority)
SELECT 'universal', 'Gifts', 'activity', 'Surprise Gift for {contact_name}', 'Plan a thoughtful surprise', 'Get or make something special for them', 'Think of something they mentioned wanting or needing, or create something personal that shows you were thinking of them', 1
WHERE NOT EXISTS (SELECT 1 FROM reminder_templates WHERE contact_temperament = 'universal' AND love_tank_category = 'Gifts' AND action_type = 'activity');

-- Function to get active reminders for a user
CREATE OR REPLACE FUNCTION get_active_reminders(user_uuid UUID)
RETURNS TABLE (
  id UUID,
  title TEXT,
  message TEXT,
  category TEXT,
  temperament TEXT,
  frequency TEXT,
  reminder_type TEXT,
  contact_name TEXT,
  last_shown_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the requesting user matches the user_uuid
  IF auth.uid() != user_uuid THEN
    RAISE EXCEPTION 'Access denied';
  END IF;

  RETURN QUERY
  -- Get personal reminders
  SELECT 
    pr.id,
    pr.title,
    pr.message,
    pr.category,
    pr.temperament,
    pr.frequency,
    'personal'::TEXT as reminder_type,
    NULL::TEXT as contact_name,
    pr.last_shown_at,
    pr.created_at
  FROM personal_reminders pr
  WHERE pr.user_id = user_uuid 
    AND pr.is_active = true
    AND (
      pr.last_shown_at IS NULL 
      OR (
        pr.frequency = 'daily' AND pr.last_shown_at < NOW() - INTERVAL '1 day'
      )
      OR (
        pr.frequency = 'weekly' AND pr.last_shown_at < NOW() - INTERVAL '1 week'
      )
      OR (
        pr.frequency = 'monthly' AND pr.last_shown_at < NOW() - INTERVAL '1 month'
      )
    )
  
  UNION ALL
  
  -- Get circle reminders
  SELECT 
    cr.id,
    cr.title,
    cr.message,
    cr.category,
    cr.temperament,
    cr.frequency,
    'circle'::TEXT as reminder_type,
    cc.name as contact_name,
    cr.last_shown_at,
    cr.created_at
  FROM circle_reminders cr
  LEFT JOIN circle_contacts cc ON cr.contact_id = cc.id
  WHERE cr.user_id = user_uuid 
    AND cr.is_active = true
    AND (
      cr.last_shown_at IS NULL 
      OR (
        cr.frequency = 'daily' AND cr.last_shown_at < NOW() - INTERVAL '1 day'
      )
      OR (
        cr.frequency = 'weekly' AND cr.last_shown_at < NOW() - INTERVAL '1 week'
      )
      OR (
        cr.frequency = 'monthly' AND cr.last_shown_at < NOW() - INTERVAL '1 month'
      )
    )
  ORDER BY created_at DESC
  LIMIT 10;
END;
$$;

-- Function to get personalized circle reminders
CREATE OR REPLACE FUNCTION get_personalized_circle_reminders(user_uuid UUID)
RETURNS TABLE (
  id UUID,
  contact_id UUID,
  contact_name TEXT,
  contact_temperament TEXT,
  love_tank_category TEXT,
  love_tank_level INTEGER,
  action_type TEXT,
  title TEXT,
  message TEXT,
  action_suggestion TEXT,
  sample_message TEXT,
  priority INTEGER
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the requesting user matches the user_uuid
  IF auth.uid() != user_uuid THEN
    RAISE EXCEPTION 'Access denied';
  END IF;

  RETURN QUERY
  SELECT 
    gen_random_uuid() as id,
    cc.id as contact_id,
    cc.name as contact_name,
    COALESCE(cc.dominant_temperament, 'universal') as contact_temperament,
    ltc.name as love_tank_category,
    COALESCE(clt.current_level, 50) as love_tank_level,
    rt.action_type,
    REPLACE(rt.title_template, '{contact_name}', cc.name) as title,
    rt.message_template as message,
    rt.action_suggestion,
    REPLACE(COALESCE(rt.sample_message, ''), '{contact_name}', cc.name) as sample_message,
    rt.priority
  FROM circle_contacts cc
  CROSS JOIN love_tank_categories ltc
  LEFT JOIN circle_love_tanks clt ON clt.contact_id = cc.id AND clt.category_id = ltc.id
  LEFT JOIN reminder_templates rt ON (
    rt.contact_temperament = COALESCE(cc.dominant_temperament, 'universal') 
    OR rt.contact_temperament = 'universal'
  ) AND rt.love_tank_category = ltc.name
  LEFT JOIN reminder_actions ra ON ra.contact_id = cc.id 
    AND ra.love_tank_category_id = ltc.id 
    AND ra.completed_at > NOW() - INTERVAL '1 week'
  WHERE cc.user_id = user_uuid
    AND rt.id IS NOT NULL
    AND ra.id IS NULL -- Don't show if action was taken recently
    AND (
      ltc.temperament = 'universal' 
      OR ltc.temperament = COALESCE(cc.dominant_temperament, 'universal')
    )
    AND COALESCE(clt.current_level, 50) < 80 -- Only show for love tanks that need filling
  ORDER BY 
    COALESCE(clt.current_level, 50) ASC, -- Prioritize lowest love tanks
    rt.priority ASC,
    cc.name ASC
  LIMIT 20;
END;
$$;

-- Function to get contact love tanks
CREATE OR REPLACE FUNCTION get_contact_love_tanks(contact_uuid UUID)
RETURNS TABLE (
  category_name TEXT,
  category_icon TEXT,
  current_level INTEGER,
  target_level INTEGER,
  last_filled_at TIMESTAMPTZ,
  days_since_filled INTEGER
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  contact_temperament TEXT;
  contact_user_id UUID;
BEGIN
  -- Get contact info and verify access
  SELECT cc.dominant_temperament, cc.user_id 
  INTO contact_temperament, contact_user_id
  FROM circle_contacts cc 
  WHERE cc.id = contact_uuid;

  IF contact_user_id != auth.uid() THEN
    RAISE EXCEPTION 'Access denied';
  END IF;

  RETURN QUERY
  SELECT 
    ltc.name as category_name,
    ltc.icon as category_icon,
    COALESCE(clt.current_level, 50) as current_level,
    COALESCE(clt.target_level, 80) as target_level,
    clt.last_filled_at,
    CASE 
      WHEN clt.last_filled_at IS NULL THEN NULL
      ELSE EXTRACT(DAY FROM NOW() - clt.last_filled_at)::INTEGER
    END as days_since_filled
  FROM love_tank_categories ltc
  LEFT JOIN circle_love_tanks clt ON clt.category_id = ltc.id AND clt.contact_id = contact_uuid
  WHERE ltc.temperament = 'universal' 
    OR ltc.temperament = COALESCE(contact_temperament, 'universal')
  ORDER BY ltc.priority ASC, ltc.name ASC;
END;
$$;

-- Function to record reminder action
CREATE OR REPLACE FUNCTION record_reminder_action(
  contact_uuid UUID,
  love_tank_category_name TEXT,
  action_type_param TEXT,
  action_taken_param TEXT,
  love_tank_increase_param INTEGER
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  category_id UUID;
  contact_user_id UUID;
BEGIN
  -- Verify contact belongs to user
  SELECT cc.user_id INTO contact_user_id
  FROM circle_contacts cc 
  WHERE cc.id = contact_uuid;

  IF contact_user_id != auth.uid() THEN
    RAISE EXCEPTION 'Access denied';
  END IF;

  -- Get category ID
  SELECT ltc.id INTO category_id
  FROM love_tank_categories ltc
  WHERE ltc.name = love_tank_category_name;

  IF category_id IS NULL THEN
    RAISE EXCEPTION 'Love tank category not found';
  END IF;

  -- Record the action
  INSERT INTO reminder_actions (
    user_id,
    contact_id,
    love_tank_category_id,
    action_type,
    action_taken,
    love_tank_increase
  ) VALUES (
    auth.uid(),
    contact_uuid,
    category_id,
    action_type_param,
    action_taken_param,
    love_tank_increase_param
  );

  -- Update or create circle love tank
  INSERT INTO circle_love_tanks (
    user_id,
    contact_id,
    category_id,
    current_level,
    last_filled_at,
    updated_at
  ) VALUES (
    auth.uid(),
    contact_uuid,
    category_id,
    LEAST(100, 50 + love_tank_increase_param),
    NOW(),
    NOW()
  )
  ON CONFLICT (contact_id, category_id)
  DO UPDATE SET
    current_level = LEAST(100, circle_love_tanks.current_level + love_tank_increase_param),
    last_filled_at = NOW(),
    updated_at = NOW();
END;
$$;

-- Function to mark a reminder as shown
CREATE OR REPLACE FUNCTION mark_reminder_shown(reminder_id UUID, reminder_type TEXT)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF reminder_type = 'personal' THEN
    UPDATE personal_reminders 
    SET last_shown_at = NOW()
    WHERE id = reminder_id AND user_id = auth.uid();
  ELSIF reminder_type = 'circle' THEN
    UPDATE circle_reminders 
    SET last_shown_at = NOW()
    WHERE id = reminder_id AND user_id = auth.uid();
  ELSE
    RAISE EXCEPTION 'Invalid reminder type';
  END IF;
END;
$$;

-- Function to update love tank level
CREATE OR REPLACE FUNCTION update_love_tank_level(tank_id UUID, new_level INTEGER)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Validate level is within bounds
  IF new_level < 0 OR new_level > 100 THEN
    RAISE EXCEPTION 'Level must be between 0 and 100';
  END IF;

  UPDATE love_tanks 
  SET 
    current_level = new_level,
    last_filled_at = NOW(),
    updated_at = NOW()
  WHERE id = tank_id AND user_id = auth.uid();
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Love tank not found or access denied';
  END IF;
END;
$$;

-- Grant execute permissions on functions to authenticated users
GRANT EXECUTE ON FUNCTION get_active_reminders(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_personalized_circle_reminders(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_contact_love_tanks(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION record_reminder_action(UUID, TEXT, TEXT, TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION mark_reminder_shown(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION update_love_tank_level(UUID, INTEGER) TO authenticated;