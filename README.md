# Harmona - Personality Assessment & Relationship App

A comprehensive personality assessment app built with Expo and React Native that helps users understand their temperament and improve their relationships.

## Features

- **Email Verification**: Secure OTP-based email verification system
- **Personality Assessment**: 10-question assessment to determine your dominant and secondary temperaments
- **Daily Insights**: Personalized tips, quotes, and insights based on your personality type
- **Relationship Comparison**: Compare personalities with friends, family, and colleagues
- **Mood Tracking**: Daily mood logging with emoji selection
- **Circle Management**: Organize your relationships by category

## Temperament Types

The app is based on the four classical temperaments:

- **Choleric**: Natural leaders who are goal-oriented and decisive
- **Sanguine**: Enthusiastic extroverts who love social interaction  
- **Melancholic**: Deep thinkers who are thoughtful and detail-oriented
- **Phlegmatic**: Peaceful mediators who value harmony and stability

## Setup Instructions

### 1. Supabase Configuration

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API in your Supabase dashboard
3. Copy your Project URL and anon/public key
4. Update the `.env` file with your credentials:

```env
EXPO_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
```

### 2. Database Setup

Run the migration files in your Supabase SQL editor:

1. Go to the SQL Editor in your Supabase dashboard
2. First, copy and paste the contents of `supabase/migrations/20250613133320_sunny_mode.sql`
3. Then, copy and paste the contents of `supabase/migrations/add_email_verification.sql`
4. Run both migrations to create all necessary tables and policies

### 3. Authentication Setup

The app uses Supabase Auth with email/password authentication and OTP verification:

- **Email Verification**: Required for all new accounts
- **OTP System**: 6-digit codes sent via email (10-minute expiration)
- **Rate Limiting**: Max 3 verification attempts per hour, 5 attempts per code
- **Row Level Security**: Enabled on all tables
- **User Isolation**: Users can only access their own data

### 4. Email Verification Flow

1. User registers with email/password
2. System generates 6-digit OTP code
3. User receives verification email
4. User enters OTP code to verify email
5. Only verified users can access the app

### 5. Demo Mode

If Supabase isn't configured, the app runs in demo mode:

- You can explore the interface
- Authentication and email verification won't work
- Use the "Demo Credentials" button on the login screen

## Development

```bash
# Install dependencies
npm install

# Start the development server
npm run dev
```

## Project Structure

```
app/
├── (tabs)/           # Main tab navigation
│   ├── index.tsx     # Home screen with personality overview
│   ├── compare.tsx   # Personality comparison tool
│   ├── talk.tsx      # Communication features (coming soon)
│   ├── circle.tsx    # Relationship circle (coming soon)
│   └── settings.tsx  # App settings (coming soon)
├── auth/             # Authentication screens
│   ├── index.tsx     # Welcome screen
│   ├── login.tsx     # Login form
│   ├── register.tsx  # Registration form
│   ├── verify-email.tsx # Email verification with OTP
│   └── forgot-password.tsx # Password reset
├── assessment/       # Personality assessment flow
├── result/           # Assessment results display
└── index.tsx         # Splash screen and routing logic

components/           # Reusable UI components
constants/           # App constants and configurations
hooks/              # Custom React hooks
lib/                # Utilities and external service configs
types/              # TypeScript type definitions
supabase/           # Database migrations and schema
```

## Database Schema

### Core Tables

- **user_profiles**: User information and verification status
- **personality_profiles**: Assessment results and temperament data
- **email_verifications**: OTP verification tracking
- **mood_entries**: Daily mood tracking
- **daily_insights**: Personalized content by temperament
- **circle_contacts**: User's relationship network
- **assessment_responses**: Individual question responses

### Security Features

- **Row Level Security (RLS)**: Enabled on all tables
- **User Isolation**: Users can only access their own data
- **Email Verification**: Required before app access
- **Rate Limiting**: Prevents spam and abuse
- **Token Expiration**: OTP codes expire after 10 minutes

## Key Features Explained

### Email Verification System
- Secure 6-digit OTP codes
- 10-minute expiration window
- Rate limiting (3 attempts/hour, 5 attempts/code)
- Automatic cleanup of expired tokens
- Resend functionality with countdown timer

### Personality Assessment
- 10 carefully crafted questions
- Each answer maps to one of the four temperaments
- Results show dominant and secondary temperament percentages

### Daily Insights
- Personalized content based on your temperament
- Includes insights, practical tips, and inspirational quotes
- Content refreshes daily

### Relationship Comparison
- Compare your personality with contacts in your circle
- Shows compatibility scores and potential conflict areas
- Provides communication tips and best times to talk

### Mood Tracking
- Simple emoji-based mood logging
- Tracks emotional patterns over time
- Integrates with personality insights

## Technologies Used

- **Expo Router**: File-based navigation
- **Supabase**: Backend, authentication, and database
- **React Native**: Cross-platform mobile development
- **TypeScript**: Type safety and better developer experience
- **Lucide React Native**: Beautiful, consistent icons
- **PostgreSQL**: Robust database with RLS security

## Security Considerations

- All user data is isolated using Row Level Security
- Email verification prevents unauthorized access
- Rate limiting prevents abuse of verification system
- Sensitive operations require authentication
- Environment variables protect API credentials

## Contributing

This is a production-ready app template. Feel free to customize and extend it for your specific needs.

## License

MIT License - feel free to use this code for your own projects.