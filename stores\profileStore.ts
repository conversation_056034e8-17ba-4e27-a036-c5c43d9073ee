import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  supabase,
  isSupabaseConfigured,
  getDemoCredentials,
} from '../lib/supabase';
import { useAuthStore } from './authStore';
import { TemperamentType } from '../types/personality';

// Define the user profile interface
interface UserProfile {
  id: string;
  user_id: string;
  email: string;
  full_name: string;
  avatar_url?: string;
  bio?: string;
  assessment_completed: boolean;
  email_verified: boolean;
  created_at: string;
  updated_at: string;
}

// Define the personality profile interface
interface PersonalityProfile {
  id: string;
  user_id: string;
  primary_temperament: TemperamentType;
  secondary_temperament: TemperamentType;
  choleric_score: number;
  sanguine_score: number;
  melancholic_score: number;
  phlegmatic_score: number;
  last_updated: string;
}

interface ProfileState {
  // State
  profile: UserProfile | null;
  personalityProfile: PersonalityProfile | null;
  isLoading: boolean;
  error: string | null;
  isUploading: boolean;
  isUpdating: boolean;

  // Actions
  fetchProfile: () => Promise<void>;
  fetchPersonalityProfile: () => Promise<void>;
  updateProfile: (profileData: Partial<UserProfile>) => Promise<void>;
  uploadAvatar: (uri: string) => Promise<string | null>;
  updatePersonalityProfile: (
    personalityData: Partial<PersonalityProfile>
  ) => Promise<void>;
  setAssessmentCompleted: (completed: boolean) => Promise<void>;
  clearError: () => void;
  resetProfileState: () => void;
  loadDemoProfile: () => void;
}

export const useProfileStore = create<ProfileState>()(
  persist(
    (set, get) => ({
      profile: null,
      personalityProfile: null,
      isLoading: false,
      error: null,
      isUploading: false,
      isUpdating: false,

      fetchProfile: async () => {
        try {
          set({ isLoading: true, error: null });

          // If in demo mode, load demo profile
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            get().loadDemoProfile();
            return;
          }

          const userId = useAuthStore.getState().user?.id;

          if (!userId) {
            set({
              error: 'User not authenticated',
              isLoading: false,
            });
            return;
          }

          const { data, error } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('user_id', userId)
            .single();

          if (error) {
            set({
              error: error.message,
              isLoading: false,
            });
            return;
          }

          set({
            profile: data,
            isLoading: false,
          });

          // If profile indicates assessment is completed, fetch personality profile
          if (data.assessment_completed) {
            get().fetchPersonalityProfile();
          }
        } catch (error: any) {
          set({
            error: error.message || 'Failed to fetch profile',
            isLoading: false,
          });
        }
      },

      fetchPersonalityProfile: async () => {
        try {
          set({ isLoading: true, error: null });

          // If in demo mode, load demo personality profile
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            // Demo personality profile is loaded in loadDemoProfile
            set({ isLoading: false });
            return;
          }

          const userId = useAuthStore.getState().user?.id;

          if (!userId) {
            set({
              error: 'User not authenticated',
              isLoading: false,
            });
            return;
          }

          const { data, error } = await supabase
            .from('personality_profiles')
            .select('*')
            .eq('user_id', userId)
            .single();

          if (error) {
            set({
              error: error.message,
              isLoading: false,
            });
            return;
          }

          set({
            personalityProfile: data,
            isLoading: false,
          });
        } catch (error: any) {
          set({
            error: error.message || 'Failed to fetch personality profile',
            isLoading: false,
          });
        }
      },

      updateProfile: async (profileData) => {
        try {
          set({ isUpdating: true, error: null });

          // If in demo mode, just update the local state
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            set({
              profile: { ...get().profile, ...profileData } as UserProfile,
              isUpdating: false,
            });
            return;
          }

          const userId = useAuthStore.getState().user?.id;

          if (!userId) {
            set({
              error: 'User not authenticated',
              isUpdating: false,
            });
            return;
          }

          const { error } = await supabase
            .from('user_profiles')
            .update({
              ...profileData,
              updated_at: new Date().toISOString(),
            })
            .eq('user_id', userId);

          if (error) {
            set({
              error: error.message,
              isUpdating: false,
            });
            return;
          }

          // Update local state with the new profile data
          set({
            profile: {
              ...get().profile,
              ...profileData,
              updated_at: new Date().toISOString(),
            } as UserProfile,
            isUpdating: false,
          });
        } catch (error: any) {
          set({
            error: error.message || 'Failed to update profile',
            isUpdating: false,
          });
        }
      },

      uploadAvatar: async (uri) => {
        try {
          set({ isUploading: true, error: null });

          // If in demo mode, just update the local state with the URI
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            set({
              profile: { ...get().profile, avatar_url: uri } as UserProfile,
              isUploading: false,
            });
            return uri;
          }

          const userId = useAuthStore.getState().user?.id;

          if (!userId) {
            set({
              error: 'User not authenticated',
              isUploading: false,
            });
            return null;
          }

          // First, fetch the image file
          const response = await fetch(uri);
          const blob = await response.blob();

          // Generate a unique filename
          const filename = `avatar-${userId}-${new Date().getTime()}.jpg`;
          const filePath = `avatars/${filename}`;

          // Upload to Supabase Storage
          const { error: uploadError } = await supabase.storage
            .from('profiles')
            .upload(filePath, blob);

          if (uploadError) {
            set({
              error: uploadError.message,
              isUploading: false,
            });
            return null;
          }

          // Get the public URL for the uploaded file
          const { data: publicUrlData } = supabase.storage
            .from('profiles')
            .getPublicUrl(filePath);

          const publicUrl = publicUrlData.publicUrl;

          // Update the profile with the new avatar URL
          await get().updateProfile({ avatar_url: publicUrl });

          set({ isUploading: false });
          return publicUrl;
        } catch (error: any) {
          set({
            error: error.message || 'Failed to upload avatar',
            isUploading: false,
          });
          return null;
        }
      },

      updatePersonalityProfile: async (personalityData) => {
        try {
          set({ isUpdating: true, error: null });

          // If in demo mode, just update the local state
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            set({
              personalityProfile: {
                ...get().personalityProfile,
                ...personalityData,
                last_updated: new Date().toISOString(),
              } as PersonalityProfile,
              isUpdating: false,
            });
            return;
          }

          const userId = useAuthStore.getState().user?.id;

          if (!userId) {
            set({
              error: 'User not authenticated',
              isUpdating: false,
            });
            return;
          }

          // Check if personality profile exists
          const { data: existingProfile, error: fetchError } = await supabase
            .from('personality_profiles')
            .select('id')
            .eq('user_id', userId)
            .maybeSingle();

          if (fetchError) {
            set({
              error: fetchError.message,
              isUpdating: false,
            });
            return;
          }

          let error;

          if (existingProfile) {
            // Update existing profile
            const { error: updateError } = await supabase
              .from('personality_profiles')
              .update({
                ...personalityData,
                last_updated: new Date().toISOString(),
              })
              .eq('user_id', userId);

            error = updateError;
          } else {
            // First force delete any potentially hidden profiles
            console.log(
              '🧹 Calling force_delete_personality_profiles to ensure clean state...'
            );
            const { error: deleteError } = await supabase.rpc(
              'force_delete_personality_profiles',
              { user_id_param: userId }
            );

            if (deleteError) {
              console.error('❌ Error during profile cleanup:', deleteError);
              set({
                error: `Failed to clean up profiles: ${deleteError.message}`,
                isUpdating: false,
              });
              return;
            }

            console.log(
              '✅ Profile cleanup successful, creating new profile...'
            );

            // Create new profile
            const { error: insertError } = await supabase
              .from('personality_profiles')
              .insert({
                user_id: userId,
                ...personalityData,
                last_updated: new Date().toISOString(),
              });

            error = insertError;
          }

          if (error) {
            set({
              error: error.message,
              isUpdating: false,
            });
            return;
          }

          // Update local state with the new personality data
          set({
            personalityProfile: {
              ...get().personalityProfile,
              ...personalityData,
              user_id: userId,
              last_updated: new Date().toISOString(),
            } as PersonalityProfile,
            isUpdating: false,
          });

          // If this was the first time setting personality data, update assessment completed status
          if (!get().profile?.assessment_completed) {
            await get().setAssessmentCompleted(true);
          }
        } catch (error: any) {
          set({
            error: error.message || 'Failed to update personality profile',
            isUpdating: false,
          });
        }
      },

      setAssessmentCompleted: async (completed) => {
        try {
          set({ isUpdating: true, error: null });

          // If in demo mode, just update the local state
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            set({
              profile: {
                ...get().profile,
                assessment_completed: completed,
                updated_at: new Date().toISOString(),
              } as UserProfile,
              isUpdating: false,
            });
            return;
          }

          const userId = useAuthStore.getState().user?.id;

          if (!userId) {
            set({
              error: 'User not authenticated',
              isUpdating: false,
            });
            return;
          }

          const { error } = await supabase
            .from('user_profiles')
            .update({
              assessment_completed: completed,
              updated_at: new Date().toISOString(),
            })
            .eq('user_id', userId);

          if (error) {
            set({
              error: error.message,
              isUpdating: false,
            });
            return;
          }

          // Update local state
          set({
            profile: {
              ...get().profile,
              assessment_completed: completed,
              updated_at: new Date().toISOString(),
            } as UserProfile,
            isUpdating: false,
          });
        } catch (error: any) {
          set({
            error: error.message || 'Failed to update assessment status',
            isUpdating: false,
          });
        }
      },

      clearError: () => set({ error: null }),

      resetProfileState: () => {
        set({
          profile: null,
          personalityProfile: null,
          isLoading: false,
          error: null,
          isUploading: false,
          isUpdating: false,
        });
      },

      loadDemoProfile: () => {
        const demo = getDemoCredentials();
        const now = new Date().toISOString();

        set({
          profile: {
            id: 'demo-profile-id',
            user_id: 'demo-user-id',
            email: demo.email,
            full_name: demo.fullName,
            avatar_url:
              'https://ui-avatars.com/api/?name=Demo+User&background=0062FF&color=fff',
            bio: 'This is a demo user profile for testing the Harmona app.',
            assessment_completed: true,
            email_verified: true,
            created_at: now,
            updated_at: now,
          },
          personalityProfile: {
            id: 'demo-personality-id',
            user_id: 'demo-user-id',
            primary_temperament: 'sanguine',
            secondary_temperament: 'choleric',
            choleric_score: 30,
            sanguine_score: 40,
            melancholic_score: 15,
            phlegmatic_score: 15,
            last_updated: now,
          },
          isLoading: false,
        });
      },
    }),
    {
      name: 'profile-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        profile: state.profile,
        personalityProfile: state.personalityProfile,
      }),
    }
  )
);

// Initialize profile state when auth is ready
export const initializeProfile = async () => {
  const { user, isDemo } = useAuthStore.getState();
  const { fetchProfile, loadDemoProfile } = useProfileStore.getState();

  if (user) {
    if (isDemo) {
      loadDemoProfile();
    } else {
      await fetchProfile();
    }
  }
};
