export type TemperamentType = 'choleric' | 'sanguine' | 'melancholic' | 'phlegmatic';

export interface PersonalityProfile {
  id: string;
  user_id: string;
  dominant_temperament: TemperamentType;
  secondary_temperament: TemperamentType;
  dominant_percentage: number;
  secondary_percentage: number;
  assessment_completed_at: string;
  created_at: string;
}

export interface AssessmentQuestion {
  id: number;
  question: string;
  answers: AssessmentAnswer[];
}

export interface AssessmentAnswer {
  text: string;
  temperament: TemperamentType;
  value: number;
}

export interface MoodEntry {
  id: string;
  user_id: string;
  emoji: string;
  description?: string;
  created_at: string;
}

export interface DailyInsight {
  id: string;
  temperament: TemperamentType;
  insight: string;
  tip: string;
  quote: string;
  created_at: string;
}

export interface UserProfile {
  id: string;
  full_name: string;
  email: string;
  email_verified: boolean;
  has_completed_assessment: boolean;
  created_at: string;
}

export interface CircleContact {
  id: string;
  user_id: string;
  name: string;
  email?: string;
  phone?: string;
  category: 'family' | 'work' | 'friends' | 'other';
  dominant_temperament?: TemperamentType;
  secondary_temperament?: TemperamentType;
  created_at: string;
}

export interface EmailVerification {
  id: string;
  user_id: string;
  email: string;
  verification_code: string;
  expires_at: string;
  verified_at?: string;
  attempts: number;
  created_at: string;
}

export interface Subgreeting {
  id: string;
  temperament: TemperamentType;
  message: string;
  mood: '😄' | '🙂' | '😐' | '😕' | '😢' | 'neutral';
  time_of_day: 'morning' | 'afternoon' | 'evening' | 'any';
  created_at: string;
}

export interface AISubgreetingRequest {
  dominant_temperament: string;
  secondary_temperament: string;
  dominant_percentage: number;
  secondary_percentage: number;
  current_mood: string;
  time_of_day: 'morning' | 'afternoon' | 'evening' | 'any';
  user_name?: string;
}

export interface AISubgreetingResponse {
  greeting: string;
  mood_acknowledgment: string;
  personality_insight: string;
  combined_message: string;
}

export interface PersonalityTrait {
  trait_name: string;
  temperament: TemperamentType;
  description?: string;
}

export interface SavedInsight {
  id: string;
  user_id: string;
  insight_type: 'insight' | 'tip' | 'quote';
  content: string;
  temperament: TemperamentType;
  saved_at: string;
  created_at: string;
}

export interface LoveTankCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  created_at: string;
}

export interface LoveTank {
  id: string;
  user_id: string;
  category_id: string;
  current_level: number;
  last_filled_at?: string;
  created_at: string;
  updated_at: string;
  category?: LoveTankCategory;
}

export interface PersonalReminder {
  id: string;
  user_id: string;
  title: string;
  message: string;
  category: 'self-care' | 'growth' | 'health' | 'creativity' | 'goals' | 'mindfulness';
  temperament: TemperamentType | 'universal';
  frequency: 'daily' | 'weekly' | 'monthly';
  is_active: boolean;
  last_shown_at?: string;
  created_at: string;
}

export interface CircleReminder {
  id: string;
  user_id: string;
  contact_id?: string;
  title: string;
  message: string;
  category: 'connection' | 'appreciation' | 'support' | 'quality-time' | 'communication';
  temperament: TemperamentType | 'universal';
  frequency: 'daily' | 'weekly' | 'monthly';
  is_active: boolean;
  last_shown_at?: string;
  created_at: string;
  contact_name?: string;
}

export interface ActiveReminder {
  id: string;
  title: string;
  message: string;
  category: string;
  reminder_type: 'personal' | 'circle';
  contact_name?: string;
  frequency: string;
  last_shown_at?: string;
}