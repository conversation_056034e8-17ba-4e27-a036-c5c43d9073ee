/*
  # Create comparison history table

  1. New Tables
    - `comparison_history`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to user_profiles)
      - `comparison_data` (jsonb, stores the complete comparison result)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on `comparison_history` table
    - Add policy for users to manage their own comparison history

  3. Indexes
    - Add index on user_id for better query performance
    - Add index on created_at for chronological ordering
*/

-- Create comparison_history table
CREATE TABLE IF NOT EXISTS comparison_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  comparison_data jsonb NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Add foreign key constraint
ALTER TABLE comparison_history 
ADD CONSTRAINT comparison_history_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES user_profiles(id) ON DELETE CASCADE;

-- Enable RLS
ALTER TABLE comparison_history ENABLE ROW LEVEL SECURITY;

-- Create policy for users to manage their own comparison history
CREATE POLICY "Users can manage own comparison history"
  ON comparison_history
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS comparison_history_user_id_idx ON comparison_history(user_id);
CREATE INDEX IF NOT EXISTS comparison_history_created_at_idx ON comparison_history(created_at DESC);

-- Create function to get comparison history for a user
CREATE OR REPLACE FUNCTION get_comparison_history(user_uuid uuid)
RETURNS TABLE (
  id uuid,
  comparison_data jsonb,
  created_at timestamptz
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the requesting user matches the user_uuid
  IF auth.uid() != user_uuid THEN
    RAISE EXCEPTION 'Access denied';
  END IF;

  RETURN QUERY
  SELECT 
    ch.id,
    ch.comparison_data,
    ch.created_at
  FROM comparison_history ch
  WHERE ch.user_id = user_uuid
  ORDER BY ch.created_at DESC
  LIMIT 50;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_comparison_history(uuid) TO authenticated;