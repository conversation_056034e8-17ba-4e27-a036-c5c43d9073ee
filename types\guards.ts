/**
 * Type guards for runtime validation
 * These functions help ensure data from external sources matches our TypeScript types
 */

import { 
  TemperamentType, 
  UserProfile, 
  PersonalityProfile, 
  MoodEntry, 
  CircleContact,
  ContactCategory 
} from './database';

// Helper function to check if a value is a valid temperament
export function isTemperamentType(value: any): value is TemperamentType {
  return typeof value === 'string' && 
         ['choleric', 'sanguine', 'melancholic', 'phlegmatic'].includes(value);
}

// Helper function to check if a value is a valid contact category
export function isContactCategory(value: any): value is ContactCategory {
  return typeof value === 'string' && 
         ['family', 'work', 'friends', 'other'].includes(value);
}

// Type guard for UserProfile
export function isUserProfile(obj: any): obj is UserProfile {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.full_name === 'string' &&
    typeof obj.email === 'string' &&
    typeof obj.has_completed_assessment === 'boolean' &&
    typeof obj.created_at === 'string' &&
    typeof obj.updated_at === 'string'
  );
}

// Type guard for PersonalityProfile
export function isPersonalityProfile(obj: any): obj is PersonalityProfile {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.user_id === 'string' &&
    isTemperamentType(obj.dominant_temperament) &&
    isTemperamentType(obj.secondary_temperament) &&
    typeof obj.dominant_percentage === 'number' &&
    typeof obj.secondary_percentage === 'number' &&
    obj.dominant_percentage >= 0 && obj.dominant_percentage <= 100 &&
    obj.secondary_percentage >= 0 && obj.secondary_percentage <= 100 &&
    typeof obj.assessment_completed_at === 'string' &&
    typeof obj.created_at === 'string'
  );
}

// Type guard for MoodEntry
export function isMoodEntry(obj: any): obj is MoodEntry {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.user_id === 'string' &&
    typeof obj.emoji === 'string' &&
    typeof obj.description === 'string' &&
    typeof obj.created_at === 'string'
  );
}

// Type guard for CircleContact
export function isCircleContact(obj: any): obj is CircleContact {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.user_id === 'string' &&
    typeof obj.name === 'string' &&
    isContactCategory(obj.category) &&
    (obj.dominant_temperament === null || isTemperamentType(obj.dominant_temperament)) &&
    (obj.secondary_temperament === null || isTemperamentType(obj.secondary_temperament)) &&
    typeof obj.created_at === 'string'
  );
}

// Validation function that throws an error if validation fails
export function validateUserProfile(obj: any): UserProfile {
  if (!isUserProfile(obj)) {
    throw new Error('Invalid UserProfile object');
  }
  return obj;
}

export function validatePersonalityProfile(obj: any): PersonalityProfile {
  if (!isPersonalityProfile(obj)) {
    throw new Error('Invalid PersonalityProfile object');
  }
  return obj;
}

export function validateMoodEntry(obj: any): MoodEntry {
  if (!isMoodEntry(obj)) {
    throw new Error('Invalid MoodEntry object');
  }
  return obj;
}

export function validateCircleContact(obj: any): CircleContact {
  if (!isCircleContact(obj)) {
    throw new Error('Invalid CircleContact object');
  }
  return obj;
}

// Array validation helpers
export function validateUserProfiles(arr: any[]): UserProfile[] {
  return arr.map(validateUserProfile);
}

export function validatePersonalityProfiles(arr: any[]): PersonalityProfile[] {
  return arr.map(validatePersonalityProfile);
}

export function validateMoodEntries(arr: any[]): MoodEntry[] {
  return arr.map(validateMoodEntry);
}

export function validateCircleContacts(arr: any[]): CircleContact[] {
  return arr.map(validateCircleContact);
}

// Safe parsing functions that return null on validation failure
export function safeParseUserProfile(obj: any): UserProfile | null {
  try {
    return validateUserProfile(obj);
  } catch {
    return null;
  }
}

export function safeParsePersonalityProfile(obj: any): PersonalityProfile | null {
  try {
    return validatePersonalityProfile(obj);
  } catch {
    return null;
  }
}

export function safeParseMoodEntry(obj: any): MoodEntry | null {
  try {
    return validateMoodEntry(obj);
  } catch {
    return null;
  }
}

export function safeParseCircleContact(obj: any): CircleContact | null {
  try {
    return validateCircleContact(obj);
  } catch {
    return null;
  }
}
