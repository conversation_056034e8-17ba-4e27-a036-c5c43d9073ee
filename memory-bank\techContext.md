# Harmona Technical Context

## Technology Stack

Harmona is built using the following core technologies:

### Frontend

| Technology   | Version | Purpose                            |
| ------------ | ------- | ---------------------------------- |
| Expo SDK     | 53.0.0  | React Native development framework |
| React        | 19.0.0  | UI library                         |
| React Native | 0.79.1  | Mobile application framework       |
| TypeScript   | 5.8.3   | Type-safe JavaScript               |
| Expo Router  | 5.0.2   | File-based navigation system       |

### Backend

| Technology              | Version               | Purpose               |
| ----------------------- | --------------------- | --------------------- |
| Supabase                | 2.39.0                | Backend-as-a-Service  |
| PostgreSQL              | (managed by Supabase) | Database              |
| Supabase Auth           | (part of Supabase)    | Authentication system |
| Supabase Edge Functions | (part of Supabase)    | Serverless functions  |

### External Services

| Service    | Purpose               |
| ---------- | --------------------- |
| OpenRouter | AI content generation |

### Key Libraries

| Library                       | Version | Purpose                 |
| ----------------------------- | ------- | ----------------------- |
| @expo/vector-icons            | 14.1.0  | Icon system             |
| lucide-react-native           | 0.475.0 | Additional icon set     |
| @react-navigation/native      | 7.0.14  | Navigation base library |
| @react-navigation/bottom-tabs | 7.2.0   | Tab navigation          |
| expo-linear-gradient          | 14.1.3  | Gradient UI elements    |
| expo-notifications            | 0.30.5  | Push notifications      |
| expo-camera                   | 16.1.5  | Camera access           |
| expo-blur                     | 14.1.3  | Visual blur effects     |
| expo-haptics                  | 14.1.3  | Haptic feedback         |
| react-native-reanimated       | 3.17.4  | Advanced animations     |
| react-native-gesture-handler  | 2.24.0  | Touch handlers          |

## Development Environment

### Required Software

- Node.js (v18+)
- npm or yarn
- Expo CLI
- Git
- VSCode (recommended IDE)
- Android Studio (for Android development)
- Xcode (for iOS development, Mac only)

### Setup Process

1. **Clone the Repository**

   ```bash
   git clone <repository-url>
   cd harmona
   ```

2. **Install Dependencies**

   ```bash
   npm install
   ```

3. **Configure Environment Variables**
   Create a `.env` file in the project root with the following variables:

   ```
   EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
   EXPO_PUBLIC_OPENROUTER_API_KEY=your_openrouter_key
   ```

4. **Run the Development Server**
   ```bash
   npm run dev
   ```

### VSCode Extensions

- ESLint
- Prettier
- React Native Tools
- Expo Tools
- TypeScript Hero
- GitLens
- Error Lens

### Recommended Development Workflows

#### Local Development

1. Use Expo Go on physical devices for rapid development
2. Use Expo Dev Client for testing native modules
3. Use Web browser for quick UI iterations

#### Testing

1. Manual testing via Expo Go
2. Component testing with Jest (future implementation)
3. E2E testing with Detox (future implementation)

## Architecture Decisions

### Why Expo?

Expo was chosen as the development framework for several reasons:

1. **Rapid Development**: Expo provides a comprehensive set of tools and libraries that speed up development.
2. **OTA Updates**: Ability to push updates without going through the app store review process.
3. **Managed Workflow**: Simplified native module handling and configuration.
4. **Expo Router**: First-class file-based routing similar to Next.js.
5. **Cross-Platform**: Single codebase for iOS, Android, and Web.

### Why Supabase?

Supabase was selected as the backend solution for these key benefits:

1. **All-in-One Solution**: Provides authentication, database, storage, and serverless functions.
2. **PostgreSQL**: Powerful, open-source relational database with advanced features.
3. **Row Level Security**: Built-in data security at the database level.
4. **Real-time Capabilities**: Subscribe to database changes.
5. **Auth Integration**: Complete authentication system with multiple providers.
6. **Edge Functions**: Serverless functions for backend logic.
7. **Open Source**: Can be self-hosted if needed in the future.

### TypeScript Over JavaScript

TypeScript was chosen to ensure:

1. **Type Safety**: Catch errors at compile time rather than runtime.
2. **Better IDE Integration**: Enhanced autocomplete and documentation.
3. **Maintainability**: Easier to understand and refactor code.
4. **Scalability**: Better suited for larger codebases and team collaboration.

### File-Based Routing with Expo Router

Expo Router was selected because:

1. **Intuitive Organization**: File system mirrors the app's navigation structure.
2. **Nested Layouts**: Easy to create shared layouts with nested routes.
3. **Type Safety**: Full TypeScript support for routes.
4. **Deep Linking**: Simplified deep linking configuration.
5. **Web Compatibility**: Works well for web deployment.

## Technical Constraints

### Expo Limitations

1. **Native Module Access**: Limited access to native modules outside of Expo's ecosystem without ejecting or using development builds.
2. **Bundle Size**: Larger initial bundle size compared to bare React Native.
3. **Update Cadence**: Dependent on Expo SDK release cycle for React Native version updates.
4. **Custom Native Code**: Requires custom development builds or ejecting for truly custom native code.

### Supabase Limitations

1. **Query Complexity**: Complex queries may require multiple round-trips or SQL functions.
2. **Connection Management**: Limited control over connection pooling.
3. **Scaling**: May require careful planning for very high-scale applications.
4. **Cold Starts**: Edge functions may experience cold starts.

### React Native Limitations

1. **Native Performance**: Some complex animations or computations may not perform as well as native code.
2. **Platform Differences**: Some features may behave differently between iOS and Android.
3. **Web Support**: Some mobile-specific features don't translate well to web.
4. **Native Bridge Overhead**: Communication between JS and native has overhead.

## Development Tooling

### Build System

Harmona uses EAS (Expo Application Services) for builds:

```json
// eas.json configuration structure
{
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal"
    },
    "preview": {
      "distribution": "internal"
    },
    "production": {}
  }
}
```

### Code Quality Tools

- **ESLint**: Static code analysis
- **Prettier**: Code formatting
- **TypeScript**: Static type checking

### Dependency Management

- Package management via npm
- Expo SDK manages core native dependencies
- Strict versioning for stability

### Environment Configuration

- `.env` file for local development
- EAS Secrets for production environment variables
- Configuration fallbacks for demo mode

## Database Schema Management

### Migration Strategy

- SQL migrations in Supabase/migrations directory
- Timestamped migration files
- Sequential application of migrations

### Type Safety

- TypeScript interfaces for database tables
- Manual type definitions (currently)
- Future: Automated type generation from database schema

## Authentication Flow

### Registration

1. User enters email, password, and name
2. Create account in Supabase Auth
3. Database trigger creates user profile
4. Send verification email
5. Redirect to verification screen

### Login

1. User enters credentials
2. Authenticate with Supabase Auth
3. Check if email is verified
4. Check if assessment is completed
5. Route to appropriate screen based on user state

### Email Verification

1. User receives verification email
2. Clicks link in email
3. Supabase marks email as verified
4. User can proceed with app usage

## API Integration Patterns

### Supabase API

```typescript
// Example of Supabase data fetching pattern
const fetchUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('id', userId)
    .single();

  if (error) throw error;
  return data;
};
```

### OpenRouter API

```typescript
// Example of OpenRouter API pattern
const generatePersonalizedInsight = async (
  temperament: string,
  prompt: string
) => {
  const response = await fetch(
    'https://openrouter.ai/api/v1/chat/completions',
    {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${process.env.EXPO_PUBLIC_OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3-haiku',
        messages: [
          {
            role: 'system',
            content: `You are a personality insight generator for ${temperament} temperaments.`,
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
      }),
    }
  );

  const data = await response.json();
  return data.choices[0].message.content;
};
```

## Deployment Strategy

### App Distribution

- iOS App Store deployment via EAS Build and Submit
- Google Play Store deployment via EAS Build and Submit
- TestFlight for iOS beta testing
- Internal track for Android beta testing

### Backend Deployment

- Supabase hosted instance for production
- Database migrations applied via Supabase CLI
- Edge functions deployed via Supabase CLI

### Update Strategy

- OTA updates for non-native changes via EAS Update
- Full app store updates for native dependency changes
- Database schema changes via migrations

## Security Considerations

### Authentication Security

- Email verification required
- JWT token-based authentication
- Password strength requirements
- Token refresh handling

### Data Security

- Row Level Security (RLS) on all tables
- Environment variables for sensitive information
- No sensitive data stored in client-side storage

### API Security

- API keys stored in environment variables
- Rate limiting (via Supabase and OpenRouter)
- Input validation and sanitization

## Performance Considerations

### Rendering Performance

- Use of memo and useCallback for expensive components
- Virtualized lists for long scrolling content
- Optimized re-renders via proper state management

### Network Performance

- Request batching where appropriate
- Caching strategies for repeated requests
- Offline fallbacks for critical features

### Load Time Optimization

- Asset preloading
- Lazy loading of non-critical components
- Optimized image assets

## Monitoring and Analytics

### Error Tracking

- Future implementation of error monitoring service
- Client-side error catching and reporting
- Server-side logging via Supabase

### Analytics

- Future implementation of usage analytics
- Event tracking for key user actions
- Conversion funnel analysis

### Performance Monitoring

- Future implementation of performance monitoring
- Key metrics tracking (load times, interaction times)
- Slow query detection

## Future Technical Considerations

### Scalability Plans

- Optimized database indexes for growing data
- Potential implementation of caching layer
- Horizontal scaling via Supabase

### New Features Technical Assessment

- Real-time chat functionality
- Expanded AI content generation
- Advanced analytics and insights
- Offline mode support

### Tech Stack Evolution

- Evaluation of Zustand for state management
- Potential implementation of React Query for data fetching
- Exploration of Tamagui for improved UI performance

This technical context document should guide all development decisions and help maintain consistency across the Harmona codebase.
