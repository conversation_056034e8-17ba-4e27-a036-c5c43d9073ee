import { TemperamentType } from '../types/personality';

export const TEMPERAMENT_COLORS = {
  choleric: '#EF4444', // Red
  sanguine: '#F97316', // Orange
  melancholic: '#3B82F6', // Blue
  phlegmatic: '#10B981', // Green
} as const;

// Note: TEMPERAMENT_KEYWORDS is now deprecated in favor of dynamic database traits
// This is kept for backward compatibility but should not be used for new features
export const TEMPERAMENT_KEYWORDS = {
  choleric: ['Leader', 'Decisive', 'Ambitious'],
  sanguine: ['Enthusiastic', 'Social', 'Optimistic'],
  melancholic: ['Thoughtful', 'Analytical', 'Creative'],
  phlegmatic: ['Calm', 'Loyal', 'Peaceful'],
} as const;

export const TEMPERAMENT_DESCRIPTIONS = {
  choleric: 'Natural leaders who are goal-oriented and decisive',
  sanguine: 'Enthusiastic extroverts who love social interaction',
  melancholic: 'Deep thinkers who are thoughtful and detail-oriented',
  phlegmatic: 'Peaceful mediators who value harmony and stability',
} as const;

export const ADJACENT_TEMPERAMENTS = {
  choleric: ['sanguine', 'melancholic'],
  sanguine: ['choleric', 'phlegmatic'],
  melancholic: ['choleric', 'phlegmatic'],
  phlegmatic: ['sanguine', 'melancholic'],
} as const;