-- Drop the existing function to recreate with randomization
DROP FUNCTION IF EXISTS get_mixed_personality_traits(text, text, integer, integer);

-- Create enhanced function with randomization for dynamic trait selection
CREATE OR REPLACE FUNCTION get_mixed_personality_traits(
  dominant_temp text,
  secondary_temp text,
  dominant_pct integer,
  secondary_pct integer
)
RETURNS TABLE(trait_name text, temperament text, description text)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  dominant_count integer;
  secondary_count integer;
  total_traits integer := 5; -- Maximum traits to show
BEGIN
  -- Calculate trait distribution based on percentages
  -- More sophisticated calculation for better distribution
  dominant_count := GREATEST(1, LEAST(4, ROUND(dominant_pct / 20.0)));
  secondary_count := GREATEST(1, LEAST(4, ROUND(secondary_pct / 20.0)));
  
  -- Ensure we show exactly 5 traits total for consistency
  IF dominant_count + secondary_count > total_traits THEN
    -- Prioritize dominant temperament but ensure secondary gets at least 1
    IF dominant_count >= 4 THEN
      dominant_count := 4;
      secondary_count := 1;
    ELSE
      secondary_count := total_traits - dominant_count;
    END IF;
  ELSIF dominant_count + secondary_count < total_traits THEN
    -- If we have fewer than 5, add more to dominant
    dominant_count := total_traits - secondary_count;
  END IF;

  -- Return randomized dominant temperament traits
  RETURN QUERY
  SELECT t.trait_name, t.temperament, t.description
  FROM traits t
  WHERE t.temperament = dominant_temp
  ORDER BY RANDOM() -- Randomize selection for variety
  LIMIT dominant_count;

  -- Return randomized secondary temperament traits
  IF secondary_count > 0 THEN
    RETURN QUERY
    SELECT t.trait_name, t.temperament, t.description
    FROM traits t
    WHERE t.temperament = secondary_temp
    ORDER BY RANDOM() -- Randomize selection for variety
    LIMIT secondary_count;
  END IF;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_mixed_personality_traits(text, text, integer, integer) TO authenticated;

-- Add more trait variety to ensure good randomization
-- Additional Choleric traits (using priority values 1-10 to comply with constraint)
INSERT INTO traits (temperament, trait_name, description, priority) VALUES
('choleric', 'Determined', 'Persistent and unwavering in pursuit of goals', 9),
('choleric', 'Strategic', 'Plans and executes with tactical precision', 10),
('choleric', 'Bold', 'Takes risks and makes courageous decisions', 8),
('choleric', 'Efficient', 'Maximizes productivity and minimizes waste', 7);

-- Additional Sanguine traits
INSERT INTO traits (temperament, trait_name, description, priority) VALUES
('sanguine', 'Inspiring', 'Motivates and uplifts others naturally', 9),
('sanguine', 'Adaptable', 'Flexible and quick to adjust to change', 10),
('sanguine', 'Communicative', 'Excellent at expressing ideas and connecting', 8),
('sanguine', 'Adventurous', 'Seeks new experiences and excitement', 7);

-- Additional Melancholic traits
INSERT INTO traits (temperament, trait_name, description, priority) VALUES
('melancholic', 'Detailed', 'Pays careful attention to specifics and nuances', 9),
('melancholic', 'Philosophical', 'Contemplates deeper meanings and purposes', 10),
('melancholic', 'Artistic', 'Expresses creativity through various mediums', 8),
('melancholic', 'Conscientious', 'Acts with moral integrity and responsibility', 7);

-- Additional Phlegmatic traits
INSERT INTO traits (temperament, trait_name, description, priority) VALUES
('phlegmatic', 'Harmonious', 'Creates and maintains peaceful environments', 9),
('phlegmatic', 'Consistent', 'Maintains steady behavior and performance', 10),
('phlegmatic', 'Gentle', 'Approaches others with kindness and care', 8),
('phlegmatic', 'Balanced', 'Maintains equilibrium in thoughts and actions', 7);