/*
  # Create user profile trigger

  1. New Functions
    - `handle_new_user()` - Automatically creates user profile when auth user is created
  
  2. New Triggers  
    - `on_auth_user_created` - Triggers profile creation after user signup
  
  3. Security
    - Function runs with security definer privileges
    - Handles user metadata extraction safely
*/

-- <PERSON>reate function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  INSERT INTO public.user_profiles (
    id,
    full_name,
    email,
    email_verified,
    has_completed_assessment
  )
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.email, ''),
    COALESCE(NEW.email_confirmed_at IS NOT NULL, false),
    false
  );
  RETURN NEW;
END;
$$;

-- <PERSON>reate trigger to automatically create user profile
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();