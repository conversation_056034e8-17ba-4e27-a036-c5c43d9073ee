interface OpenRouterResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
  error?: {
    message: string;
    code: string;
  };
}

interface AssessmentQuestion {
  question: string;
  answers: string[];
  questionNumber: number;
  category: 'initial' | 'confirmation' | 'comparison' | 'tiebreaker';
  temperamentOrder: number[]; // Maps answer positions to temperament indices
}

interface AssessmentQuestions {
  questions: AssessmentQuestion[];
  instructions: string;
}

interface SubgreetingRequest {
  dominant_temperament: string;
  secondary_temperament: string;
  dominant_percentage: number;
  secondary_percentage: number;
  current_mood: string;
  time_of_day: 'morning' | 'afternoon' | 'evening' | 'any';
  user_name?: string;
}

interface SubgreetingResponse {
  greeting: string;
  mood_acknowledgment: string;
  personality_insight: string;
  combined_message: string;
}

interface PersonalizedInsightRequest {
  temperament: string;
  secondary_temperament?: string;
  current_mood?: string;
  insight_type: 'insight' | 'tip' | 'quote';
}

interface PersonalizedInsightResponse {
  content: string;
  type: 'insight' | 'tip' | 'quote';
}

const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

export class OpenRouterService {
  private apiKey: string;

  constructor() {
    this.apiKey = process.env.EXPO_PUBLIC_OPENROUTER_API_KEY || '';
  }

  isConfigured(): boolean {
    return !!this.apiKey && 
           this.apiKey !== 'your-openrouter-api-key-here' && 
           this.apiKey.length > 10;
  }

  async generatePersonalizedInsight(request: PersonalizedInsightRequest): Promise<PersonalizedInsightResponse> {
    if (!this.isConfigured()) {
      console.warn('OpenRouter API key not configured, using fallback insight');
      throw new Error('OpenRouter API key not configured');
    }

    const temperamentTraits = {
      choleric: 'decisive, driven, ambitious, leadership-focused, goal-oriented',
      sanguine: 'enthusiastic, social, optimistic, energetic, people-focused',
      melancholic: 'thoughtful, analytical, creative, introspective, detail-oriented',
      phlegmatic: 'calm, peaceful, supportive, steady, harmony-seeking'
    };

    const insightTypePrompts = {
      insight: 'Create a profound psychological insight about their temperament that helps them understand themselves better',
      tip: 'Provide a practical, actionable tip they can use today to leverage their temperament strengths',
      quote: 'Generate an inspiring, original quote that resonates with their temperament and current situation'
    };

    const prompt = `You are a wise personality coach. Create personalized content for someone with ${request.temperament} temperament.

TEMPERAMENT TRAITS: ${temperamentTraits[request.temperament as keyof typeof temperamentTraits]}
${request.secondary_temperament ? `SECONDARY TRAITS: ${temperamentTraits[request.secondary_temperament as keyof typeof temperamentTraits]}` : ''}
${request.current_mood ? `CURRENT MOOD: ${request.current_mood}` : ''}

TASK: ${insightTypePrompts[request.insight_type]}

REQUIREMENTS:
- Keep it concise (1-2 sentences for insights/tips, 1 sentence for quotes)
- Make it personally relevant to their temperament
- Use warm, encouraging tone
- Be specific and actionable (for tips)
- Be profound and meaningful (for insights)
- Be inspiring and memorable (for quotes)

Return ONLY a JSON object:
{
  "content": "[your ${request.insight_type} here]",
  "type": "${request.insight_type}"
}`;

    try {
      console.log(`🤖 Generating AI ${request.insight_type} for ${request.temperament}...`);
      
      const response = await fetch(OPENROUTER_API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://harmona.app',
          'X-Title': 'Harmona Personalized Insights'
        },
        body: JSON.stringify({
          model: 'google/gemini-2.5-flash-preview-05-20',
          messages: [
            {
              role: 'system',
              content: 'You are an expert personality coach. Always return valid JSON without markdown formatting. Create personalized, meaningful content.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.8,
          max_tokens: 300,
          top_p: 0.9
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ OpenRouter API error:', response.status, errorText);
        throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
      }

      const data: OpenRouterResponse = await response.json();
      
      if (data.error) {
        console.error('❌ OpenRouter API returned error:', data.error);
        throw new Error(`OpenRouter API error: ${data.error.message}`);
      }

      const content = data.choices[0]?.message?.content;

      if (!content) {
        console.error('❌ No content received from OpenRouter API');
        throw new Error('No content received from OpenRouter API');
      }

      // Clean and parse JSON response
      let cleanContent = content.trim();
      
      if (cleanContent.startsWith('```json')) {
        cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanContent.startsWith('```')) {
        cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      const jsonStart = cleanContent.indexOf('{');
      const jsonEnd = cleanContent.lastIndexOf('}');
      
      if (jsonStart === -1 || jsonEnd === -1) {
        throw new Error('No valid JSON found in AI response');
      }

      const jsonContent = cleanContent.substring(jsonStart, jsonEnd + 1);

      let insightData: PersonalizedInsightResponse;
      try {
        insightData = JSON.parse(jsonContent);
      } catch (parseError) {
        console.error('❌ JSON parsing error:', parseError);
        throw new Error('Failed to parse AI response as JSON');
      }
      
      if (!insightData || !insightData.content) {
        throw new Error('Invalid response format: missing content');
      }

      console.log(`🎉 AI ${request.insight_type} generated:`, insightData.content);
      return insightData;

    } catch (error) {
      console.error(`💥 Error generating personalized ${request.insight_type}:`, error);
      throw error;
    }
  }

  async generatePersonalizedSubgreeting(request: SubgreetingRequest): Promise<SubgreetingResponse> {
    if (!this.isConfigured()) {
      console.warn('OpenRouter API key not configured, using fallback greeting');
      throw new Error('OpenRouter API key not configured');
    }

    const moodDescriptions = {
      '😄': 'extremely happy and energetic',
      '🙂': 'happy and positive',
      '😐': 'neutral and balanced',
      '😕': 'somewhat sad or concerned',
      '😢': 'very sad or emotional',
      'neutral': 'balanced and centered'
    };

    const timeContexts = {
      'morning': 'starting their day',
      'afternoon': 'in the middle of their day',
      'evening': 'winding down for the evening',
      'any': 'going about their day'
    };

    const prompt = `You are a compassionate AI personality coach. Create a very short, intimate greeting for a user.

USER PROFILE:
- Dominant Temperament: ${request.dominant_temperament} (${request.dominant_percentage}%)
- Secondary Temperament: ${request.secondary_temperament} (${request.secondary_percentage}%)
- Current Mood: ${moodDescriptions[request.current_mood as keyof typeof moodDescriptions] || 'neutral'}
- Time Context: ${timeContexts[request.time_of_day]}

TEMPERAMENT TRAITS:
- Choleric: decisive, driven, ambitious, leadership-focused
- Sanguine: enthusiastic, social, optimistic, energetic
- Melancholic: thoughtful, analytical, creative, introspective
- Phlegmatic: calm, peaceful, supportive, steady

TASK: Create a personalized subgreeting that:
1. Is EXACTLY 5-7 words maximum
2. Acknowledges their mood empathetically
3. Reflects their dominant temperament
4. Feels warm and personal
5. Uses simple, natural language

STYLE REQUIREMENTS:
- Keep it extremely concise (5-7 words only)
- Use warm, encouraging tone
- Be specific to their temperament + mood
- Avoid complex phrases
- Make it feel personally crafted
- Use present tense

EXAMPLES OF GOOD SHORT SUBGREETINGS:
- "channeling your choleric drive beautifully"
- "radiating that perfect sanguine joy"
- "processing today with melancholic wisdom"
- "bringing gentle phlegmatic strength forward"
- "conquering challenges with fierce determination"
- "spreading infectious happiness and optimism"
- "finding beauty in thoughtful moments"
- "creating peace through steady presence"

Return ONLY a JSON object:
{
  "combined_message": "[5-7 word personalized greeting]"
}

The combined_message should be the complete greeting that will be displayed to the user.`;

    try {
      console.log('🤖 Generating concise AI subgreeting...');
      
      const response = await fetch(OPENROUTER_API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://harmona.app',
          'X-Title': 'Harmona Personalized Greetings'
        },
        body: JSON.stringify({
          model: 'google/gemini-2.5-flash-preview-05-20',
          messages: [
            {
              role: 'system',
              content: 'You are an expert at creating very short, personalized greetings. Always return valid JSON without markdown formatting. Keep greetings to exactly 5-7 words.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.8,
          max_tokens: 200,
          top_p: 0.9
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ OpenRouter API error:', response.status, errorText);
        throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
      }

      const data: OpenRouterResponse = await response.json();
      
      if (data.error) {
        console.error('❌ OpenRouter API returned error:', data.error);
        throw new Error(`OpenRouter API error: ${data.error.message}`);
      }

      const content = data.choices[0]?.message?.content;

      if (!content) {
        console.error('❌ No content received from OpenRouter API');
        throw new Error('No content received from OpenRouter API');
      }

      console.log('📝 Raw AI subgreeting response received, parsing...');

      // Clean the content - remove any markdown formatting
      let cleanContent = content.trim();
      
      // Remove markdown code blocks if present
      if (cleanContent.startsWith('```json')) {
        cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanContent.startsWith('```')) {
        cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      // Find JSON object boundaries
      const jsonStart = cleanContent.indexOf('{');
      const jsonEnd = cleanContent.lastIndexOf('}');
      
      if (jsonStart === -1 || jsonEnd === -1) {
        console.error('❌ No valid JSON found in response');
        throw new Error('No valid JSON found in AI response');
      }

      const jsonContent = cleanContent.substring(jsonStart, jsonEnd + 1);

      // Parse the JSON response
      let subgreetingData: SubgreetingResponse;
      try {
        subgreetingData = JSON.parse(jsonContent);
      } catch (parseError) {
        console.error('❌ JSON parsing error:', parseError);
        console.error('Content that failed to parse:', jsonContent);
        throw new Error('Failed to parse AI response as JSON');
      }
      
      // Validate the response structure
      if (!subgreetingData || typeof subgreetingData !== 'object') {
        throw new Error('Invalid response format: not an object');
      }

      if (!subgreetingData.combined_message) {
        throw new Error('Invalid response format: missing combined_message');
      }

      // Ensure the message is concise (fallback if AI didn't follow instructions)
      const words = subgreetingData.combined_message.split(' ');
      if (words.length > 8) {
        subgreetingData.combined_message = words.slice(0, 7).join(' ');
      }

      console.log('🎉 Concise AI subgreeting generated:', subgreetingData.combined_message);
      return subgreetingData;

    } catch (error) {
      console.error('💥 Error generating personalized subgreeting:', error);
      
      // Provide more specific error messages
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Network error: Unable to connect to AI service');
      } else if (error instanceof SyntaxError) {
        throw new Error('AI response format error: Invalid JSON received');
      } else {
        throw error;
      }
    }
  }

  // Generate a random but balanced temperament order for each question
  private generateTemperamentOrder(): number[] {
    const orders = [
      [0, 1, 2, 3], // Choleric, Sanguine, Melancholic, Phlegmatic
      [1, 2, 3, 0], // Sanguine, Melancholic, Phlegmatic, Choleric
      [2, 3, 0, 1], // Melancholic, Phlegmatic, Choleric, Sanguine
      [3, 0, 1, 2], // Phlegmatic, Choleric, Sanguine, Melancholic
      [0, 2, 1, 3], // Choleric, Melancholic, Sanguine, Phlegmatic
      [1, 3, 0, 2], // Sanguine, Phlegmatic, Choleric, Melancholic
      [2, 0, 3, 1], // Melancholic, Choleric, Phlegmatic, Sanguine
      [3, 1, 2, 0], // Phlegmatic, Sanguine, Melancholic, Choleric
      [0, 3, 2, 1], // Choleric, Phlegmatic, Melancholic, Sanguine
      [1, 0, 3, 2], // Sanguine, Choleric, Phlegmatic, Melancholic
    ];
    
    return orders[Math.floor(Math.random() * orders.length)];
  }

  async generateAssessmentQuestions(): Promise<AssessmentQuestions> {
    if (!this.isConfigured()) {
      console.warn('OpenRouter API key not configured, using fallback questions');
      throw new Error('OpenRouter API key not configured');
    }

    // Generate random temperament orders for each question
    const questionOrders = Array.from({ length: 10 }, () => this.generateTemperamentOrder());
    
    const prompt = `You are a personality quiz generator based on the Four Temperaments: Choleric, Melancholic, Phlegmatic, and Sanguine.

🎯 Your job: Generate a set of 10 adaptive multiple-choice personality questions with MIXED answer positions.

CRITICAL REQUIREMENT: The temperament types must be mixed across answer positions (A, B, C, D) for each question according to this specific order:

Question 1: Position A = ${this.getTemperamentName(questionOrders[0][0])}, B = ${this.getTemperamentName(questionOrders[0][1])}, C = ${this.getTemperamentName(questionOrders[0][2])}, D = ${this.getTemperamentName(questionOrders[0][3])}
Question 2: Position A = ${this.getTemperamentName(questionOrders[1][0])}, B = ${this.getTemperamentName(questionOrders[1][1])}, C = ${this.getTemperamentName(questionOrders[1][2])}, D = ${this.getTemperamentName(questionOrders[1][3])}
Question 3: Position A = ${this.getTemperamentName(questionOrders[2][0])}, B = ${this.getTemperamentName(questionOrders[2][1])}, C = ${this.getTemperamentName(questionOrders[2][2])}, D = ${this.getTemperamentName(questionOrders[2][3])}
Question 4: Position A = ${this.getTemperamentName(questionOrders[3][0])}, B = ${this.getTemperamentName(questionOrders[3][1])}, C = ${this.getTemperamentName(questionOrders[3][2])}, D = ${this.getTemperamentName(questionOrders[3][3])}
Question 5: Position A = ${this.getTemperamentName(questionOrders[4][0])}, B = ${this.getTemperamentName(questionOrders[4][1])}, C = ${this.getTemperamentName(questionOrders[4][2])}, D = ${this.getTemperamentName(questionOrders[4][3])}
Question 6: Position A = ${this.getTemperamentName(questionOrders[5][0])}, B = ${this.getTemperamentName(questionOrders[5][1])}, C = ${this.getTemperamentName(questionOrders[5][2])}, D = ${this.getTemperamentName(questionOrders[5][3])}
Question 7: Position A = ${this.getTemperamentName(questionOrders[6][0])}, B = ${this.getTemperamentName(questionOrders[6][1])}, C = ${this.getTemperamentName(questionOrders[6][2])}, D = ${this.getTemperamentName(questionOrders[6][3])}
Question 8: Position A = ${this.getTemperamentName(questionOrders[7][0])}, B = ${this.getTemperamentName(questionOrders[7][1])}, C = ${this.getTemperamentName(questionOrders[7][2])}, D = ${this.getTemperamentName(questionOrders[7][3])}
Question 9: Position A = ${this.getTemperamentName(questionOrders[8][0])}, B = ${this.getTemperamentName(questionOrders[8][1])}, C = ${this.getTemperamentName(questionOrders[8][2])}, D = ${this.getTemperamentName(questionOrders[8][3])}
Question 10: Position A = ${this.getTemperamentName(questionOrders[9][0])}, B = ${this.getTemperamentName(questionOrders[9][1])}, C = ${this.getTemperamentName(questionOrders[9][2])}, D = ${this.getTemperamentName(questionOrders[9][3])}

Temperament Characteristics:
- Choleric: decisive, goal-oriented, leadership-focused, direct, ambitious
- Sanguine: enthusiastic, social, optimistic, spontaneous, people-focused
- Melancholic: thoughtful, analytical, detail-oriented, perfectionist, introspective
- Phlegmatic: calm, peaceful, supportive, diplomatic, steady

Each question must:
1. Be casual, friendly, and emotionally intelligent
2. Include 4 answer choices that match the specified temperament order for that question
3. Avoid explicitly mentioning the personality type names
4. Be relatable and natural (no clinical or overly formal phrasing)
5. Cover different life scenarios (work, relationships, stress, decision-making, etc.)

Quiz Logic:
1–4: Determine dominant temperament by scoring each answer
5–6: Confirm dominant type with specific, introspective questions
7–9: Compare dominant type with adjacent types
10: Ask a tie-breaker question or final confirmation

Return ONLY a valid JSON object in this exact format (no markdown, no extra text):
{
  "instructions": "Brief explanation of how the assessment works",
  "questions": [
    {
      "questionNumber": 1,
      "category": "initial",
      "question": "Your question text here",
      "answers": [
        "Answer aligned with ${this.getTemperamentName(questionOrders[0][0])}",
        "Answer aligned with ${this.getTemperamentName(questionOrders[0][1])}",
        "Answer aligned with ${this.getTemperamentName(questionOrders[0][2])}",
        "Answer aligned with ${this.getTemperamentName(questionOrders[0][3])}"
      ],
      "temperamentOrder": [${questionOrders[0].join(', ')}]
    }
  ]
}

Make sure:
- Questions 1-4 have category "initial"
- Questions 5-6 have category "confirmation"  
- Questions 7-9 have category "comparison"
- Question 10 has category "tiebreaker"
- Each question follows the exact temperament order specified above
- All questions are engaging and relatable
- Answers clearly differentiate the temperaments without being obvious
- Return valid JSON only, no markdown formatting`;

    try {
      console.log('🤖 Generating AI assessment questions with mixed temperament positions...');
      
      const response = await fetch(OPENROUTER_API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://harmona.app',
          'X-Title': 'Harmona Personality Assessment'
        },
        body: JSON.stringify({
          model: 'google/gemini-2.5-flash-preview-05-20',
          messages: [
            {
              role: 'system',
              content: 'You are a professional personality assessment generator. Return only valid JSON responses without any markdown formatting or additional text. Follow the temperament order specifications exactly.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.8, // Higher temperature for more variety
          max_tokens: 6000,
          top_p: 1
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ OpenRouter API error:', response.status, errorText);
        throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
      }

      const data: OpenRouterResponse = await response.json();
      
      if (data.error) {
        console.error('❌ OpenRouter API returned error:', data.error);
        throw new Error(`OpenRouter API error: ${data.error.message}`);
      }

      const content = data.choices[0]?.message?.content;

      if (!content) {
        console.error('❌ No content received from OpenRouter API');
        throw new Error('No content received from OpenRouter API');
      }

      console.log('📝 Raw AI response received, parsing...');

      // Clean the content - remove any markdown formatting
      let cleanContent = content.trim();
      
      // Remove markdown code blocks if present
      if (cleanContent.startsWith('```json')) {
        cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanContent.startsWith('```')) {
        cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      // Find JSON object boundaries
      const jsonStart = cleanContent.indexOf('{');
      const jsonEnd = cleanContent.lastIndexOf('}');
      
      if (jsonStart === -1 || jsonEnd === -1) {
        console.error('❌ No valid JSON found in response');
        throw new Error('No valid JSON found in AI response');
      }

      const jsonContent = cleanContent.substring(jsonStart, jsonEnd + 1);

      // Parse the JSON response
      let assessmentData;
      try {
        assessmentData = JSON.parse(jsonContent);
      } catch (parseError) {
        console.error('❌ JSON parsing error:', parseError);
        console.error('Content that failed to parse:', jsonContent);
        throw new Error('Failed to parse AI response as JSON');
      }
      
      // Validate the response structure
      if (!assessmentData || typeof assessmentData !== 'object') {
        throw new Error('Invalid response format: not an object');
      }

      if (!assessmentData.questions || !Array.isArray(assessmentData.questions)) {
        throw new Error('Invalid response format: missing questions array');
      }

      if (assessmentData.questions.length !== 10) {
        console.warn(`⚠️ Expected exactly 10 questions, got ${assessmentData.questions.length}`);
        throw new Error(`Expected exactly 10 questions, got ${assessmentData.questions.length}`);
      }

      // Validate and fix each question
      for (let i = 0; i < assessmentData.questions.length; i++) {
        const q = assessmentData.questions[i];
        
        if (!q.question || typeof q.question !== 'string') {
          throw new Error(`Invalid question format at index ${i}: missing question text`);
        }
        
        if (!q.answers || !Array.isArray(q.answers) || q.answers.length !== 4) {
          throw new Error(`Invalid question format at index ${i}: must have exactly 4 answers`);
        }
        
        if (!q.category || typeof q.category !== 'string') {
          throw new Error(`Invalid question format at index ${i}: missing category`);
        }
        
        // Ensure questionNumber is set correctly
        q.questionNumber = i + 1;
        
        // Add the temperament order for this question
        q.temperamentOrder = questionOrders[i];
        
        console.log(`✅ Question ${i + 1}: Temperament order [${q.temperamentOrder.map(idx => this.getTemperamentName(idx)).join(', ')}]`);
      }

      // Ensure instructions exist
      if (!assessmentData.instructions) {
        assessmentData.instructions = "This assessment uses AI-generated questions to determine your personality type based on the Four Temperaments model. Each question is uniquely crafted with mixed answer positions to ensure accurate results.";
      }

      console.log('🎉 AI assessment questions generated successfully with mixed temperament positions');
      return assessmentData;

    } catch (error) {
      console.error('💥 Error generating assessment questions:', error);
      
      // Provide more specific error messages
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Network error: Unable to connect to AI service');
      } else if (error instanceof SyntaxError) {
        throw new Error('AI response format error: Invalid JSON received');
      } else {
        throw error;
      }
    }
  }

  private getTemperamentName(index: number): string {
    const names = ['Choleric', 'Sanguine', 'Melancholic', 'Phlegmatic'];
    return names[index] || 'Unknown';
  }

  // Enhanced fallback questions with mixed temperament positions
  getFallbackQuestions(): AssessmentQuestions {
    const fallbackOrders = [
      [0, 1, 2, 3], // Q1: Choleric, Sanguine, Melancholic, Phlegmatic
      [2, 0, 3, 1], // Q2: Melancholic, Choleric, Phlegmatic, Sanguine
      [1, 3, 0, 2], // Q3: Sanguine, Phlegmatic, Choleric, Melancholic
      [3, 2, 1, 0], // Q4: Phlegmatic, Melancholic, Sanguine, Choleric
      [0, 2, 1, 3], // Q5: Choleric, Melancholic, Sanguine, Phlegmatic
      [1, 0, 3, 2], // Q6: Sanguine, Choleric, Phlegmatic, Melancholic
      [2, 3, 0, 1], // Q7: Melancholic, Phlegmatic, Choleric, Sanguine
      [3, 1, 2, 0], // Q8: Phlegmatic, Sanguine, Melancholic, Choleric
      [0, 3, 1, 2], // Q9: Choleric, Phlegmatic, Sanguine, Melancholic
      [1, 2, 3, 0], // Q10: Sanguine, Melancholic, Phlegmatic, Choleric
    ];

    return {
      instructions: "This assessment uses proven psychological principles to determine your personality type based on the Four Temperaments model. Answer positions are mixed to ensure accurate results.",
      questions: [
        {
          questionNumber: 1,
          category: "initial",
          question: "When you're given a group task, how do you usually respond?",
          answers: [
            "I take the lead and delegate efficiently", // Choleric
            "I crack jokes and keep the energy light", // Sanguine
            "I quietly plan out the best structure", // Melancholic
            "I support others and keep things peaceful" // Phlegmatic
          ],
          temperamentOrder: fallbackOrders[0]
        },
        {
          questionNumber: 2,
          category: "initial",
          question: "How do you prefer to spend your free time?",
          answers: [
            "Reading, reflecting, or working on creative projects", // Melancholic
            "Pursuing ambitious goals or challenges", // Choleric
            "Relaxing at home or enjoying quiet activities", // Phlegmatic
            "Socializing with friends and meeting new people" // Sanguine
          ],
          temperamentOrder: fallbackOrders[1]
        },
        {
          questionNumber: 3,
          category: "initial",
          question: "When making important decisions, you tend to:",
          answers: [
            "Consider how it affects relationships and seek input", // Sanguine
            "Take time to ensure everyone is comfortable with the choice", // Phlegmatic
            "Decide quickly based on logic and efficiency", // Choleric
            "Analyze all possibilities thoroughly before choosing" // Melancholic
          ],
          temperamentOrder: fallbackOrders[2]
        },
        {
          questionNumber: 4,
          category: "initial",
          question: "In stressful situations, you typically:",
          answers: [
            "Remain calm and help others stay centered", // Phlegmatic
            "Withdraw to think and process emotions", // Melancholic
            "Try to lighten the mood and stay optimistic", // Sanguine
            "Take charge and push through obstacles" // Choleric
          ],
          temperamentOrder: fallbackOrders[3]
        },
        {
          questionNumber: 5,
          category: "confirmation",
          question: "Your ideal work environment would be:",
          answers: [
            "Fast-paced with clear goals and results", // Choleric
            "Quiet with time for deep focus", // Melancholic
            "Collaborative with lots of interaction", // Sanguine
            "Stable with supportive colleagues" // Phlegmatic
          ],
          temperamentOrder: fallbackOrders[4]
        },
        {
          questionNumber: 6,
          category: "confirmation",
          question: "When someone disagrees with you, you:",
          answers: [
            "Try to find common ground through discussion", // Sanguine
            "Present your case confidently and directly", // Choleric
            "Avoid conflict and seek compromise", // Phlegmatic
            "Consider their perspective carefully" // Melancholic
          ],
          temperamentOrder: fallbackOrders[5]
        },
        {
          questionNumber: 7,
          category: "comparison",
          question: "You feel most energized when:",
          answers: [
            "Having meaningful conversations or insights", // Melancholic
            "Maintaining harmony and helping others", // Phlegmatic
            "Accomplishing challenging goals", // Choleric
            "Connecting with others and sharing experiences" // Sanguine
          ],
          temperamentOrder: fallbackOrders[6]
        },
        {
          questionNumber: 8,
          category: "comparison",
          question: "Your approach to planning is:",
          answers: [
            "Prefer simple, low-pressure approaches", // Phlegmatic
            "Keep it flexible and adapt as you go", // Sanguine
            "Plan thoroughly with attention to detail", // Melancholic
            "Set clear objectives and execute efficiently" // Choleric
          ],
          temperamentOrder: fallbackOrders[7]
        },
        {
          questionNumber: 9,
          category: "comparison",
          question: "In social gatherings, you usually:",
          answers: [
            "Network and discuss goals or achievements", // Choleric
            "Listen more than talk and support others", // Phlegmatic
            "Mingle freely and enjoy meeting everyone", // Sanguine
            "Have deep conversations with a few people" // Melancholic
          ],
          temperamentOrder: fallbackOrders[8]
        },
        {
          questionNumber: 10,
          category: "tiebreaker",
          question: "What motivates you most?",
          answers: [
            "Fun, variety, and positive relationships", // Sanguine
            "Meaning, authenticity, and personal growth", // Melancholic
            "Security, stability, and helping others", // Phlegmatic
            "Achievement, success, and recognition" // Choleric
          ],
          temperamentOrder: fallbackOrders[9]
        }
      ]
    };
  }
}

export const openRouterService = new OpenRouterService();