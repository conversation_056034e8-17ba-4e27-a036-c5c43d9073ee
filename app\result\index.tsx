import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { supabase } from '@/lib/supabase';
import { PersonalityProfile } from '@/types/personality';
import { TEMPERAMENT_COLORS, TEMPERAMENT_DESCRIPTIONS } from '@/constants/temperaments';
import { CircleCheck as CheckCircle, ArrowRight } from 'lucide-react-native';

export default function ResultScreen() {
  const [personalityProfile, setPersonalityProfile] = useState<PersonalityProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const mounted = useRef(false);

  useEffect(() => {
    mounted.current = true;
    loadPersonalityProfile();
    return () => {
      mounted.current = false;
    };
  }, []);

  const loadPersonalityProfile = async () => {
    if (!mounted.current) return;
    
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: profile } = await supabase
        .from('personality_profiles')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (mounted.current && profile) {
        setPersonalityProfile(profile);
      }
    } catch (error) {
      console.error('Error loading personality profile:', error);
    } finally {
      if (mounted.current) {
        setLoading(false);
      }
    }
  };

  const handleContinue = () => {
    if (!mounted.current) return;
    router.replace('/(tabs)');
  };

  if (!mounted.current || loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading your results...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!personalityProfile) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Unable to load your personality profile.</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadPersonalityProfile}>
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const getTemperamentColor = (temperament: string) => {
    return TEMPERAMENT_COLORS[temperament as keyof typeof TEMPERAMENT_COLORS] || '#6B7280';
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <CheckCircle size={48} color="#10B981" />
          <Text style={styles.title}>Your Personality Profile</Text>
          <Text style={styles.subtitle}>
            Discover your unique temperament combination
          </Text>
        </View>

        <View style={styles.profileCard}>
          <View style={styles.temperamentSection}>
            <Text style={styles.sectionTitle}>Dominant Temperament</Text>
            <View style={styles.temperamentItem}>
              <View 
                style={[
                  styles.temperamentIndicator,
                  { backgroundColor: getTemperamentColor(personalityProfile.dominant_temperament) }
                ]}
              />
              <View style={styles.temperamentInfo}>
                <Text style={styles.temperamentName}>
                  {personalityProfile.dominant_temperament.charAt(0).toUpperCase() + 
                   personalityProfile.dominant_temperament.slice(1)}
                </Text>
                <Text style={styles.temperamentPercentage}>
                  {personalityProfile.dominant_percentage}%
                </Text>
              </View>
            </View>
            <Text style={styles.temperamentDescription}>
              {TEMPERAMENT_DESCRIPTIONS[personalityProfile.dominant_temperament as keyof typeof TEMPERAMENT_DESCRIPTIONS]}
            </Text>
          </View>

          <View style={styles.divider} />

          <View style={styles.temperamentSection}>
            <Text style={styles.sectionTitle}>Secondary Temperament</Text>
            <View style={styles.temperamentItem}>
              <View 
                style={[
                  styles.temperamentIndicator,
                  { backgroundColor: getTemperamentColor(personalityProfile.secondary_temperament) }
                ]}
              />
              <View style={styles.temperamentInfo}>
                <Text style={styles.temperamentName}>
                  {personalityProfile.secondary_temperament.charAt(0).toUpperCase() + 
                   personalityProfile.secondary_temperament.slice(1)}
                </Text>
                <Text style={styles.temperamentPercentage}>
                  {personalityProfile.secondary_percentage}%
                </Text>
              </View>
            </View>
            <Text style={styles.temperamentDescription}>
              {TEMPERAMENT_DESCRIPTIONS[personalityProfile.secondary_temperament as keyof typeof TEMPERAMENT_DESCRIPTIONS]}
            </Text>
          </View>
        </View>

        <View style={styles.insightCard}>
          <Text style={styles.insightTitle}>What This Means</Text>
          <Text style={styles.insightText}>
            Your personality is a unique blend of {personalityProfile.dominant_temperament} and {personalityProfile.secondary_temperament} traits. 
            This combination influences how you interact with others, make decisions, and approach life's challenges.
          </Text>
          <Text style={styles.insightText}>
            In the app, you'll receive personalized insights, tips, and guidance based on your specific temperament combination 
            to help you better understand yourself and improve your relationships.
          </Text>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity style={styles.continueButton} onPress={handleContinue}>
          <Text style={styles.continueButtonText}>Continue to App</Text>
          <ArrowRight size={20} color="#ffffff" />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#ffffff',
    fontWeight: '600',
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1F2937',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  profileCard: {
    backgroundColor: '#ffffff',
    marginHorizontal: 24,
    borderRadius: 16,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    marginBottom: 24,
  },
  temperamentSection: {
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  temperamentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  temperamentIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  temperamentInfo: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  temperamentName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  temperamentPercentage: {
    fontSize: 18,
    fontWeight: '700',
    color: '#3B82F6',
  },
  temperamentDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  divider: {
    height: 1,
    backgroundColor: '#E5E7EB',
    marginVertical: 16,
  },
  insightCard: {
    backgroundColor: '#F0F9FF',
    marginHorizontal: 24,
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
  },
  insightTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E40AF',
    marginBottom: 12,
  },
  insightText: {
    fontSize: 14,
    color: '#1E40AF',
    lineHeight: 20,
    marginBottom: 12,
  },
  footer: {
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  continueButton: {
    backgroundColor: '#3B82F6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});