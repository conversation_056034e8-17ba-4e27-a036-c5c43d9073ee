-- Simplify love tanks schema to match requirements
-- New structure: id, temperament, love_tank, created_at, updated_at

-- Drop existing functions that depend on the old schema
DROP FUNCTION IF EXISTS get_active_reminders(UUID);
DROP FUNCTION IF EXISTS mark_reminder_shown(UUID, TEXT);
DROP FUNCTION IF EXISTS update_love_tank_level(UUID, INTEGER);
DROP FUNCTION IF EXISTS get_personalized_circle_reminders(UUID);
DROP FUNCTION IF EXISTS get_contact_love_tanks(UUID);
DROP FUNCTION IF EXISTS record_reminder_action(UUID, TEXT, TEXT, TEXT, INTEGER);

-- Drop existing tables to recreate with new schema
DROP TABLE IF EXISTS reminder_actions CASCADE;
DROP TABLE IF EXISTS reminder_templates CASCADE;
DROP TABLE IF EXISTS circle_love_tanks CASCADE;
DROP TABLE IF EXISTS love_tanks CASCADE;
DROP TABLE IF EXISTS love_tank_categories CASCADE;

-- Create simplified love_tanks table
CREATE TABLE love_tanks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  temperament TEXT NOT NULL CHECK (temperament = ANY (ARRAY['sanguine'::text, 'melancholic'::text, 'choleric'::text, 'phlegmatic'::text])),
  love_tank TEXT NOT NULL CHECK (love_tank = ANY (ARRAY['Gifts'::text, 'Quality Time'::text, 'Acts of Service'::text, 'Physical Touch'::text])),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(temperament, love_tank)
);

-- Enable RLS on love_tanks table
ALTER TABLE love_tanks ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for love_tanks
CREATE POLICY "Anyone can read love tanks" ON love_tanks FOR SELECT TO authenticated USING (true);

-- Insert the 4 specific temperament-love tank combinations
INSERT INTO love_tanks (temperament, love_tank) VALUES
('sanguine', 'Gifts'),
('melancholic', 'Quality Time'),
('choleric', 'Acts of Service'),
('phlegmatic', 'Physical Touch')
ON CONFLICT (temperament, love_tank) DO NOTHING;

-- Create a simple function to get love tanks
CREATE OR REPLACE FUNCTION get_love_tanks()
RETURNS TABLE (
  id UUID,
  temperament TEXT,
  love_tank TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    lt.id,
    lt.temperament,
    lt.love_tank,
    lt.created_at,
    lt.updated_at
  FROM love_tanks lt
  ORDER BY lt.temperament, lt.love_tank;
END;
$$;

-- Create function to get love tank by temperament
CREATE OR REPLACE FUNCTION get_love_tank_by_temperament(user_temperament TEXT)
RETURNS TABLE (
  id UUID,
  temperament TEXT,
  love_tank TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    lt.id,
    lt.temperament,
    lt.love_tank,
    lt.created_at,
    lt.updated_at
  FROM love_tanks lt
  WHERE lt.temperament = user_temperament;
END;
$$;

-- Grant execute permissions on functions to authenticated users
GRANT EXECUTE ON FUNCTION get_love_tanks() TO authenticated;
GRANT EXECUTE ON FUNCTION get_love_tank_by_temperament(TEXT) TO authenticated;