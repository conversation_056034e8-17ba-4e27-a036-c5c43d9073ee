-- Drop all existing functions first to avoid return type conflicts
DROP FUNCTION IF EXISTS get_active_reminders(UUID);
DROP FUNCTION IF EXISTS mark_reminder_shown(UUID, TEXT);
DROP FUNCTION IF EXISTS update_love_tank_level(UUID, INTEGER);

-- Function to get active reminders for a user
CREATE OR REPLACE FUNCTION get_active_reminders(user_uuid UUID)
RETURNS TABLE (
  id UUID,
  title TEXT,
  message TEXT,
  category TEXT,
  temperament TEXT,
  frequency TEXT,
  reminder_type TEXT,
  contact_name TEXT,
  last_shown_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the requesting user matches the user_uuid
  IF auth.uid() != user_uuid THEN
    RAISE EXCEPTION 'Access denied';
  END IF;

  RETURN QUERY
  -- Get personal reminders
  SELECT 
    pr.id,
    pr.title,
    pr.message,
    pr.category,
    pr.temperament,
    pr.frequency,
    'personal'::TEXT as reminder_type,
    NULL::TEXT as contact_name,
    pr.last_shown_at,
    pr.created_at
  FROM personal_reminders pr
  WHERE pr.user_id = user_uuid 
    AND pr.is_active = true
    AND (
      pr.last_shown_at IS NULL 
      OR (
        pr.frequency = 'daily' AND pr.last_shown_at < NOW() - INTERVAL '1 day'
      )
      OR (
        pr.frequency = 'weekly' AND pr.last_shown_at < NOW() - INTERVAL '1 week'
      )
      OR (
        pr.frequency = 'monthly' AND pr.last_shown_at < NOW() - INTERVAL '1 month'
      )
    )
  
  UNION ALL
  
  -- Get circle reminders
  SELECT 
    cr.id,
    cr.title,
    cr.message,
    cr.category,
    cr.temperament,
    cr.frequency,
    'circle'::TEXT as reminder_type,
    cc.name as contact_name,
    cr.last_shown_at,
    cr.created_at
  FROM circle_reminders cr
  LEFT JOIN circle_contacts cc ON cr.contact_id = cc.id
  WHERE cr.user_id = user_uuid 
    AND cr.is_active = true
    AND (
      cr.last_shown_at IS NULL 
      OR (
        cr.frequency = 'daily' AND cr.last_shown_at < NOW() - INTERVAL '1 day'
      )
      OR (
        cr.frequency = 'weekly' AND cr.last_shown_at < NOW() - INTERVAL '1 week'
      )
      OR (
        cr.frequency = 'monthly' AND cr.last_shown_at < NOW() - INTERVAL '1 month'
      )
    )
  ORDER BY created_at DESC
  LIMIT 10;
END;
$$;

-- Function to mark a reminder as shown
CREATE OR REPLACE FUNCTION mark_reminder_shown(reminder_id UUID, reminder_type TEXT)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF reminder_type = 'personal' THEN
    UPDATE personal_reminders 
    SET last_shown_at = NOW()
    WHERE id = reminder_id AND user_id = auth.uid();
  ELSIF reminder_type = 'circle' THEN
    UPDATE circle_reminders 
    SET last_shown_at = NOW()
    WHERE id = reminder_id AND user_id = auth.uid();
  ELSE
    RAISE EXCEPTION 'Invalid reminder type';
  END IF;
END;
$$;

-- Function to update love tank level
CREATE OR REPLACE FUNCTION update_love_tank_level(tank_id UUID, new_level INTEGER)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Validate level is within bounds
  IF new_level < 0 OR new_level > 100 THEN
    RAISE EXCEPTION 'Level must be between 0 and 100';
  END IF;

  UPDATE love_tanks 
  SET 
    current_level = new_level,
    last_filled_at = NOW(),
    updated_at = NOW()
  WHERE id = tank_id AND user_id = auth.uid();
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Love tank not found or access denied';
  END IF;
END;
$$;

-- Ensure proper foreign key relationships exist (in case they're missing)
DO $$
BEGIN
  -- Check if foreign key exists between love_tanks and love_tank_categories
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'love_tanks_category_id_fkey'
    AND table_name = 'love_tanks'
  ) THEN
    ALTER TABLE love_tanks 
    ADD CONSTRAINT love_tanks_category_id_fkey 
    FOREIGN KEY (category_id) REFERENCES love_tank_categories(id) ON DELETE CASCADE;
  END IF;

  -- Check if foreign key exists between love_tanks and user_profiles
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'love_tanks_user_id_fkey'
    AND table_name = 'love_tanks'
  ) THEN
    ALTER TABLE love_tanks 
    ADD CONSTRAINT love_tanks_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES user_profiles(id) ON DELETE CASCADE;
  END IF;
END $$;

-- Grant execute permissions on functions to authenticated users
GRANT EXECUTE ON FUNCTION get_active_reminders(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION mark_reminder_shown(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION update_love_tank_level(UUID, INTEGER) TO authenticated;