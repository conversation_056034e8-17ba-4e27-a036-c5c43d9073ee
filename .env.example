# Supabase Configuration
# Get these values from your Supabase project dashboard (Settings > API)
EXPO_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here

# OpenRouter AI Configuration (Optional - for AI-generated questions)
# Get your API key from https://openrouter.ai/
EXPO_PUBLIC_OPENROUTER_API_KEY=your-openrouter-api-key-here

# Instructions:
# 1. Copy this file to .env
# 2. Replace the placeholder values with your actual credentials
# 3. Never commit the .env file with real credentials to version control
