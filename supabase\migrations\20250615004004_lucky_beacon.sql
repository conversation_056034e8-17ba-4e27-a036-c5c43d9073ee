/*
  # Create love tank categories table and update love tanks

  1. New Tables
    - `love_tank_categories`
      - `id` (uuid, primary key)
      - `name` (text, unique)
      - `description` (text)
      - `icon` (text)
      - `created_at` (timestamp)

  2. Changes
    - Add `category_id` column to `love_tanks` table
    - Add foreign key constraint between `love_tanks` and `love_tank_categories`
    - Remove old `love_tank` column from `love_tanks` table
    - Add `user_id` and `current_level` columns to `love_tanks`

  3. Security
    - Enable RLS on `love_tank_categories` table
    - Add policies for authenticated users to read categories
    - Update `love_tanks` policies for user access
*/

-- Create love_tank_categories table
CREATE TABLE IF NOT EXISTS love_tank_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text UNIQUE NOT NULL,
  description text NOT NULL,
  icon text NOT NULL DEFAULT '❤️',
  created_at timestamptz DEFAULT now()
);

-- Enable RLS on love_tank_categories
ALTER TABLE love_tank_categories ENABLE ROW LEVEL SECURITY;

-- Create policy for reading love tank categories
CREATE POLICY "Anyone can read love tank categories"
  ON love_tank_categories
  FOR SELECT
  TO authenticated
  USING (true);

-- Add new columns to love_tanks table if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'love_tanks' AND column_name = 'user_id'
  ) THEN
    ALTER TABLE love_tanks ADD COLUMN user_id uuid REFERENCES user_profiles(id) ON DELETE CASCADE;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'love_tanks' AND column_name = 'category_id'
  ) THEN
    ALTER TABLE love_tanks ADD COLUMN category_id uuid REFERENCES love_tank_categories(id) ON DELETE CASCADE;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'love_tanks' AND column_name = 'current_level'
  ) THEN
    ALTER TABLE love_tanks ADD COLUMN current_level integer DEFAULT 50 CHECK (current_level >= 0 AND current_level <= 100);
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'love_tanks' AND column_name = 'last_filled_at'
  ) THEN
    ALTER TABLE love_tanks ADD COLUMN last_filled_at timestamptz;
  END IF;
END $$;

-- Insert default love tank categories
INSERT INTO love_tank_categories (name, description, icon) VALUES
  ('Quality Time', 'Spending meaningful time together, giving undivided attention', '⏰'),
  ('Physical Touch', 'Appropriate physical contact like hugs, holding hands, or gentle touches', '🤗'),
  ('Acts of Service', 'Doing helpful things for others without being asked', '🛠️'),
  ('Gifts', 'Thoughtful presents that show care and consideration', '🎁'),
  ('Words of Affirmation', 'Encouraging, supportive, and loving words', '💬')
ON CONFLICT (name) DO NOTHING;

-- Update love_tanks policies
DROP POLICY IF EXISTS "Anyone can read love tanks" ON love_tanks;
DROP POLICY IF EXISTS "Users can manage own love tanks" ON love_tanks;

CREATE POLICY "Users can read own love tanks"
  ON love_tanks
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own love tanks"
  ON love_tanks
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own love tanks"
  ON love_tanks
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own love tanks"
  ON love_tanks
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Remove old love_tank column if it exists
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'love_tanks' AND column_name = 'love_tank'
  ) THEN
    ALTER TABLE love_tanks DROP COLUMN love_tank;
  END IF;
END $$;

-- Remove old temperament column if it exists
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'love_tanks' AND column_name = 'temperament'
  ) THEN
    ALTER TABLE love_tanks DROP COLUMN temperament;
  END IF;
END $$;