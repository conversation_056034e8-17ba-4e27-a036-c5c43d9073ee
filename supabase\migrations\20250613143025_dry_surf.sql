/*
  # Add User Profile Creation Trigger

  1. New Functions
    - `handle_new_user()` - Automatically creates user profile when new user signs up

  2. New Triggers  
    - `on_auth_user_created` - Triggers profile creation on user signup

  3. Security
    - Function runs with security definer to access auth schema
    - Ensures user profiles are created automatically and consistently
*/

-- Create a function to handle new user sign-ups
CREATE OR REPLACE FUNCTION public.handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  INSERT INTO public.user_profiles (id, full_name, email, email_verified, has_completed_assessment)
  VALUES (
    NEW.id, 
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''), 
    COALESCE(NEW.email, ''), 
    false, 
    false
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop the trigger if it exists (to avoid conflicts)
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create a trigger to call the function on new user sign-ups
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();