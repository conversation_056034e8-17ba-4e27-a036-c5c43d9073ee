import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  supabase,
  isSupabaseConfigured,
  getDemoCredentials,
} from '../lib/supabase';
import { useAuthStore } from './authStore';
import { useProfileStore } from './profileStore';

// Define mood types
export type MoodLevel = 'terrible' | 'bad' | 'neutral' | 'good' | 'great';

// Define a single mood entry
export interface MoodEntry {
  id: string;
  user_id: string;
  mood_level: MoodLevel;
  description?: string;
  tags?: string[];
  created_at: string;
  related_to?: string; // Optional reference to a circle contact
}

// Define mood analytics
export interface MoodAnalytics {
  averageMood: number; // 1-5 representing terrible to great
  moodDistribution: Record<MoodLevel, number>; // Count of each mood level
  mostFrequentMood: MoodLevel;
  moodTrend: 'improving' | 'declining' | 'stable' | 'fluctuating' | null;
  timeOfDayInsights: {
    morning: MoodLevel | null;
    afternoon: MoodLevel | null;
    evening: MoodLevel | null;
    night: MoodLevel | null;
  };
  lastUpdated: string;
}

// Define the mood store state
interface MoodState {
  // State
  currentMood: MoodEntry | null;
  moodHistory: MoodEntry[];
  moodAnalytics: MoodAnalytics | null;
  isLoading: boolean;
  isSubmitting: boolean;
  isAnalyzing: boolean;
  error: string | null;
  isDemo: boolean;

  // Actions
  fetchCurrentMood: () => Promise<void>;
  fetchMoodHistory: (limit?: number) => Promise<void>;
  recordMood: (
    level: MoodLevel,
    description?: string,
    tags?: string[],
    relatedTo?: string
  ) => Promise<void>;
  updateMoodEntry: (
    moodId: string,
    updates: Partial<Omit<MoodEntry, 'id' | 'user_id' | 'created_at'>>
  ) => Promise<void>;
  deleteMoodEntry: (moodId: string) => Promise<void>;
  analyzeMoods: () => Promise<void>;
  clearError: () => void;
  resetMoodState: () => void;
  loadDemoMoods: () => void;
}

// Generate a date-based ID for demo mode
const generateDemoId = () =>
  `demo-mood-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

// Create the mood store
export const useMoodStore = create<MoodState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentMood: null,
      moodHistory: [],
      moodAnalytics: null,
      isLoading: false,
      isSubmitting: false,
      isAnalyzing: false,
      error: null,
      isDemo: false,

      // Fetch the user's current mood (most recent entry)
      fetchCurrentMood: async () => {
        try {
          set({ isLoading: true, error: null });

          // If in demo mode, load demo mood
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            get().loadDemoMoods();
            return;
          }

          const userId = useAuthStore.getState().user?.id;

          if (!userId) {
            set({
              error: 'User not authenticated',
              isLoading: false,
            });
            return;
          }

          const { data, error } = await supabase
            .from('mood_entries')
            .select('*')
            .eq('user_id', userId)
            .order('created_at', { ascending: false })
            .limit(1)
            .single();

          if (error) {
            // If no mood entries exist yet, that's ok
            if (error.code === 'PGRST116') {
              set({
                currentMood: null,
                isLoading: false,
              });
              return;
            }

            set({
              error: error.message,
              isLoading: false,
            });
            return;
          }

          set({
            currentMood: data,
            isLoading: false,
          });
        } catch (error: any) {
          set({
            error: error.message || 'Failed to fetch current mood',
            isLoading: false,
          });
        }
      },

      // Fetch the user's mood history
      fetchMoodHistory: async (limit = 30) => {
        try {
          set({ isLoading: true, error: null });

          // If in demo mode, load demo moods
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            get().loadDemoMoods();
            return;
          }

          const userId = useAuthStore.getState().user?.id;

          if (!userId) {
            set({
              error: 'User not authenticated',
              isLoading: false,
            });
            return;
          }

          const { data, error } = await supabase
            .from('mood_entries')
            .select('*')
            .eq('user_id', userId)
            .order('created_at', { ascending: false })
            .limit(limit);

          if (error) {
            set({
              error: error.message,
              isLoading: false,
            });
            return;
          }

          set({
            moodHistory: data || [],
            currentMood: data && data.length > 0 ? data[0] : null,
            isLoading: false,
          });

          // After loading history, analyze the moods
          await get().analyzeMoods();
        } catch (error: any) {
          set({
            error: error.message || 'Failed to fetch mood history',
            isLoading: false,
          });
        }
      },

      // Record a new mood entry
      recordMood: async (level, description, tags, relatedTo) => {
        try {
          set({ isSubmitting: true, error: null });

          // If in demo mode, just update the local state
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            const newMood: MoodEntry = {
              id: generateDemoId(),
              user_id: 'demo-user-id',
              mood_level: level,
              description,
              tags,
              related_to: relatedTo,
              created_at: new Date().toISOString(),
            };

            set({
              currentMood: newMood,
              moodHistory: [newMood, ...get().moodHistory.slice(0, 29)], // Keep only the last 30 entries
              isSubmitting: false,
            });

            // After recording, analyze the moods
            await get().analyzeMoods();
            return;
          }

          const userId = useAuthStore.getState().user?.id;

          if (!userId) {
            set({
              error: 'User not authenticated',
              isSubmitting: false,
            });
            return;
          }

          const newMood = {
            user_id: userId,
            mood_level: level,
            description,
            tags,
            related_to: relatedTo,
            created_at: new Date().toISOString(),
          };

          const { data, error } = await supabase
            .from('mood_entries')
            .insert(newMood)
            .select()
            .single();

          if (error) {
            set({
              error: error.message,
              isSubmitting: false,
            });
            return;
          }

          // Update local state
          set({
            currentMood: data,
            moodHistory: [data, ...get().moodHistory.slice(0, 29)], // Keep only the last 30 entries
            isSubmitting: false,
          });

          // After recording, analyze the moods
          await get().analyzeMoods();
        } catch (error: any) {
          set({
            error: error.message || 'Failed to record mood',
            isSubmitting: false,
          });
        }
      },

      // Update an existing mood entry
      updateMoodEntry: async (moodId, updates) => {
        try {
          set({ isSubmitting: true, error: null });

          // If in demo mode, just update the local state
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            const updatedHistory = get().moodHistory.map((mood) =>
              mood.id === moodId ? { ...mood, ...updates } : mood
            );

            set({
              moodHistory: updatedHistory,
              currentMood:
                get().currentMood?.id === moodId
                  ? ({ ...get().currentMood, ...updates } as MoodEntry)
                  : get().currentMood,
              isSubmitting: false,
            });

            // After updating, analyze the moods
            await get().analyzeMoods();
            return;
          }

          const userId = useAuthStore.getState().user?.id;

          if (!userId) {
            set({
              error: 'User not authenticated',
              isSubmitting: false,
            });
            return;
          }

          const { data, error } = await supabase
            .from('mood_entries')
            .update(updates)
            .eq('id', moodId)
            .eq('user_id', userId) // Security: ensure user can only update their own entries
            .select()
            .single();

          if (error) {
            set({
              error: error.message,
              isSubmitting: false,
            });
            return;
          }

          // Update local state
          const updatedHistory = get().moodHistory.map((mood) =>
            mood.id === moodId ? data : mood
          );

          set({
            moodHistory: updatedHistory,
            currentMood:
              get().currentMood?.id === moodId
                ? (data as MoodEntry)
                : get().currentMood,
            isSubmitting: false,
          });

          // After updating, analyze the moods
          await get().analyzeMoods();
        } catch (error: any) {
          set({
            error: error.message || 'Failed to update mood entry',
            isSubmitting: false,
          });
        }
      },

      // Delete a mood entry
      deleteMoodEntry: async (moodId) => {
        try {
          set({ isSubmitting: true, error: null });

          // If in demo mode, just update the local state
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            const updatedHistory = get().moodHistory.filter(
              (mood) => mood.id !== moodId
            );

            set({
              moodHistory: updatedHistory,
              currentMood:
                get().currentMood?.id === moodId
                  ? updatedHistory.length > 0
                    ? updatedHistory[0]
                    : null
                  : get().currentMood,
              isSubmitting: false,
            });

            // After deleting, analyze the moods
            await get().analyzeMoods();
            return;
          }

          const userId = useAuthStore.getState().user?.id;

          if (!userId) {
            set({
              error: 'User not authenticated',
              isSubmitting: false,
            });
            return;
          }

          const { error } = await supabase
            .from('mood_entries')
            .delete()
            .eq('id', moodId)
            .eq('user_id', userId); // Security: ensure user can only delete their own entries

          if (error) {
            set({
              error: error.message,
              isSubmitting: false,
            });
            return;
          }

          // Update local state
          const updatedHistory = get().moodHistory.filter(
            (mood) => mood.id !== moodId
          );

          set({
            moodHistory: updatedHistory,
            currentMood:
              get().currentMood?.id === moodId
                ? updatedHistory.length > 0
                  ? updatedHistory[0]
                  : null
                : get().currentMood,
            isSubmitting: false,
          });

          // After deleting, analyze the moods
          await get().analyzeMoods();
        } catch (error: any) {
          set({
            error: error.message || 'Failed to delete mood entry',
            isSubmitting: false,
          });
        }
      },

      // Analyze mood entries to generate insights
      analyzeMoods: async () => {
        try {
          set({ isAnalyzing: true, error: null });

          const { moodHistory } = get();

          // If no mood history, reset analytics
          if (!moodHistory || moodHistory.length === 0) {
            set({
              moodAnalytics: null,
              isAnalyzing: false,
            });
            return;
          }

          // Calculate mood distribution
          const distribution: Record<MoodLevel, number> = {
            terrible: 0,
            bad: 0,
            neutral: 0,
            good: 0,
            great: 0,
          };

          // Map mood levels to numeric values for average calculation
          const moodValues: Record<MoodLevel, number> = {
            terrible: 1,
            bad: 2,
            neutral: 3,
            good: 4,
            great: 5,
          };

          // Count occurrences of each mood
          moodHistory.forEach((entry) => {
            distribution[entry.mood_level]++;
          });

          // Calculate average mood
          let sum = 0;
          moodHistory.forEach((entry) => {
            sum += moodValues[entry.mood_level];
          });
          const average = sum / moodHistory.length;

          // Find most frequent mood
          let maxCount = 0;
          let mostFrequent: MoodLevel = 'neutral';

          Object.entries(distribution).forEach(([mood, count]) => {
            if (count > maxCount) {
              maxCount = count;
              mostFrequent = mood as MoodLevel;
            }
          });

          // Determine mood trend (simplified version)
          let trend:
            | 'improving'
            | 'declining'
            | 'stable'
            | 'fluctuating'
            | null = null;

          if (moodHistory.length >= 3) {
            // Get the 5 most recent moods for trend analysis
            const recentMoods = moodHistory.slice(
              0,
              Math.min(5, moodHistory.length)
            );
            const recentValues = recentMoods.map(
              (entry) => moodValues[entry.mood_level]
            );

            // Check if consistently improving or declining
            let improving = true;
            let declining = true;

            for (let i = 0; i < recentValues.length - 1; i++) {
              if (recentValues[i] <= recentValues[i + 1]) {
                declining = false;
              }
              if (recentValues[i] >= recentValues[i + 1]) {
                improving = false;
              }
            }

            if (improving) {
              trend = 'improving';
            } else if (declining) {
              trend = 'declining';
            } else {
              // Check if stable (all values are the same)
              const allSame = recentValues.every(
                (val) => val === recentValues[0]
              );

              if (allSame) {
                trend = 'stable';
              } else {
                trend = 'fluctuating';
              }
            }
          }

          // Time of day insights
          const timeOfDay = {
            morning: null as MoodLevel | null,
            afternoon: null as MoodLevel | null,
            evening: null as MoodLevel | null,
            night: null as MoodLevel | null,
          };

          // Group entries by time of day
          const morningEntries: MoodEntry[] = [];
          const afternoonEntries: MoodEntry[] = [];
          const eveningEntries: MoodEntry[] = [];
          const nightEntries: MoodEntry[] = [];

          moodHistory.forEach((entry) => {
            const date = new Date(entry.created_at);
            const hour = date.getHours();

            if (hour >= 5 && hour < 12) {
              morningEntries.push(entry);
            } else if (hour >= 12 && hour < 17) {
              afternoonEntries.push(entry);
            } else if (hour >= 17 && hour < 21) {
              eveningEntries.push(entry);
            } else {
              nightEntries.push(entry);
            }
          });

          // Find most common mood for each time of day
          const calculateMostCommonMood = (
            entries: MoodEntry[]
          ): MoodLevel | null => {
            if (entries.length === 0) return null;

            const counts: Record<string, number> = {};
            entries.forEach((entry) => {
              counts[entry.mood_level] = (counts[entry.mood_level] || 0) + 1;
            });

            let maxCount = 0;
            let mostCommon: MoodLevel | null = null;

            Object.entries(counts).forEach(([mood, count]) => {
              if (count > maxCount) {
                maxCount = count;
                mostCommon = mood as MoodLevel;
              }
            });

            return mostCommon;
          };

          timeOfDay.morning = calculateMostCommonMood(morningEntries);
          timeOfDay.afternoon = calculateMostCommonMood(afternoonEntries);
          timeOfDay.evening = calculateMostCommonMood(eveningEntries);
          timeOfDay.night = calculateMostCommonMood(nightEntries);

          // Create analytics object
          const analytics: MoodAnalytics = {
            averageMood: parseFloat(average.toFixed(1)),
            moodDistribution: distribution,
            mostFrequentMood: mostFrequent,
            moodTrend: trend,
            timeOfDayInsights: timeOfDay,
            lastUpdated: new Date().toISOString(),
          };

          set({
            moodAnalytics: analytics,
            isAnalyzing: false,
          });
        } catch (error: any) {
          console.error('Error analyzing moods:', error);
          set({
            error: error.message || 'Failed to analyze moods',
            isAnalyzing: false,
          });
        }
      },

      // Clear any error messages
      clearError: () => {
        set({ error: null });
      },

      // Reset mood state
      resetMoodState: () => {
        set({
          currentMood: null,
          moodHistory: [],
          moodAnalytics: null,
          isLoading: false,
          isSubmitting: false,
          isAnalyzing: false,
          error: null,
        });
      },

      // Load demo mood data
      loadDemoMoods: () => {
        const demo = getDemoCredentials();
        const now = new Date();

        // Generate some realistic demo mood data spanning last 30 days
        const demoMoods: MoodEntry[] = [];
        const moodLevels: MoodLevel[] = [
          'terrible',
          'bad',
          'neutral',
          'good',
          'great',
        ];

        // Generate 15 mood entries over the last 30 days
        for (let i = 0; i < 15; i++) {
          const daysAgo = Math.floor(Math.random() * 30);
          const hoursOffset = Math.floor(Math.random() * 24);
          const entryDate = new Date(now);
          entryDate.setDate(entryDate.getDate() - daysAgo);
          entryDate.setHours(hoursOffset);

          // Ensure entries are in chronological order (newest first)
          const moodLevel =
            moodLevels[Math.floor(Math.random() * moodLevels.length)];

          // Generate some demo descriptions
          let description = '';
          if (moodLevel === 'terrible') {
            description = [
              'Had a really rough day',
              'Nothing seems to be going right',
              'Feeling overwhelmed',
            ][Math.floor(Math.random() * 3)];
          } else if (moodLevel === 'bad') {
            description = [
              'Not feeling my best today',
              'Had some challenges',
              'Kind of stressed',
            ][Math.floor(Math.random() * 3)];
          } else if (moodLevel === 'neutral') {
            description = [
              'Just an average day',
              'Nothing special to report',
              'Taking it easy',
            ][Math.floor(Math.random() * 3)];
          } else if (moodLevel === 'good') {
            description = [
              'Had a productive day',
              'Feeling positive',
              'Things are going well',
            ][Math.floor(Math.random() * 3)];
          } else if (moodLevel === 'great') {
            description = [
              'Fantastic day!',
              'Everything is going perfectly',
              'Feeling on top of the world',
            ][Math.floor(Math.random() * 3)];
          }

          // Generate some demo tags
          const allTags = [
            'work',
            'family',
            'health',
            'social',
            'personal',
            'hobby',
            'rest',
          ];
          const tagCount = Math.floor(Math.random() * 3) + 1; // 1-3 tags
          const tags: string[] = [];

          for (let j = 0; j < tagCount; j++) {
            const tag = allTags[Math.floor(Math.random() * allTags.length)];
            if (!tags.includes(tag)) {
              tags.push(tag);
            }
          }

          demoMoods.push({
            id: `demo-mood-${i}`,
            user_id: 'demo-user-id',
            mood_level: moodLevel,
            description,
            tags,
            created_at: entryDate.toISOString(),
          });
        }

        // Sort by created_at in descending order (newest first)
        demoMoods.sort(
          (a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );

        set({
          currentMood: demoMoods[0], // Most recent mood
          moodHistory: demoMoods,
          isLoading: false,
          isDemo: true,
        });

        // Analyze the demo moods
        get().analyzeMoods();
      },
    }),
    {
      name: 'mood-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        currentMood: state.currentMood,
        moodHistory: state.moodHistory,
        moodAnalytics: state.moodAnalytics,
        isDemo: state.isDemo,
      }),
    }
  )
);

// Initialize mood state when auth is ready
export const initializeMoodState = async () => {
  const { user, isDemo } = useAuthStore.getState();
  const { fetchCurrentMood, fetchMoodHistory, loadDemoMoods } =
    useMoodStore.getState();

  if (user) {
    if (isDemo) {
      loadDemoMoods();
    } else {
      await fetchCurrentMood();
      await fetchMoodHistory();
    }
  }
};
