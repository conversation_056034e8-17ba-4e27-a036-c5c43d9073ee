/**
 * Database types generated from Supabase schema
 * Based on migrations in supabase/migrations/
 *
 * This file contains the exact database schema types to ensure
 * type safety between the frontend and backend.
 */

export type TemperamentType =
  | 'choleric'
  | 'sanguine'
  | 'melancholic'
  | 'phlegmatic';
export type ContactCategory = 'family' | 'work' | 'friends' | 'other';

// Database table types (exact match to schema)
export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string; // uuid, references auth.users(id)
          full_name: string;
          email: string;
          has_completed_assessment: boolean;
          created_at: string; // timestamptz
          updated_at: string; // timestamptz
        };
        Insert: {
          id: string;
          full_name?: string;
          email?: string;
          has_completed_assessment?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          full_name?: string;
          email?: string;
          has_completed_assessment?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      personality_profiles: {
        Row: {
          id: string; // uuid
          user_id: string; // uuid, references auth.users(id)
          dominant_temperament: TemperamentType;
          secondary_temperament: TemperamentType;
          dominant_percentage: number; // integer 0-100
          secondary_percentage: number; // integer 0-100
          assessment_completed_at: string; // timestamptz
          created_at: string; // timestamptz
        };
        Insert: {
          id?: string;
          user_id: string;
          dominant_temperament: TemperamentType;
          secondary_temperament: TemperamentType;
          dominant_percentage: number;
          secondary_percentage: number;
          assessment_completed_at?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          dominant_temperament?: TemperamentType;
          secondary_temperament?: TemperamentType;
          dominant_percentage?: number;
          secondary_percentage?: number;
          assessment_completed_at?: string;
          created_at?: string;
        };
      };
      mood_entries: {
        Row: {
          id: string; // uuid
          user_id: string; // uuid, references auth.users(id)
          emoji: string;
          description: string;
          created_at: string; // timestamptz
        };
        Insert: {
          id?: string;
          user_id: string;
          emoji: string;
          description?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          emoji?: string;
          description?: string;
          created_at?: string;
        };
      };
      daily_insights: {
        Row: {
          id: string; // uuid
          temperament: TemperamentType;
          insight: string;
          tip: string;
          quote: string;
          created_at: string; // timestamptz
        };
        Insert: {
          id?: string;
          temperament: TemperamentType;
          insight: string;
          tip: string;
          quote: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          temperament?: TemperamentType;
          insight?: string;
          tip?: string;
          quote?: string;
          created_at?: string;
        };
      };
      circle_contacts: {
        Row: {
          id: string; // uuid
          user_id: string; // uuid, references auth.users(id)
          name: string;
          category: ContactCategory;
          dominant_temperament: TemperamentType | null;
          secondary_temperament: TemperamentType | null;
          created_at: string; // timestamptz
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          category: ContactCategory;
          dominant_temperament?: TemperamentType | null;
          secondary_temperament?: TemperamentType | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          category?: ContactCategory;
          dominant_temperament?: TemperamentType | null;
          secondary_temperament?: TemperamentType | null;
          created_at?: string;
        };
      };
      assessment_responses: {
        Row: {
          id: string; // uuid
          user_id: string; // uuid, references auth.users(id)
          question_number: number; // integer
          selected_answer: number; // integer
          created_at: string; // timestamptz
        };
        Insert: {
          id?: string;
          user_id: string;
          question_number: number;
          selected_answer: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          question_number?: number;
          selected_answer?: number;
          created_at?: string;
        };
      };
      subgreetings: {
        Row: {
          id: string; // uuid
          temperament: TemperamentType;
          message: string;
          mood: string; // 'energetic' | 'calm' | 'focused' | 'creative' | 'social' | 'reflective' | 'neutral'
          time_of_day: string; // 'morning' | 'afternoon' | 'evening' | 'any'
          created_at: string; // timestamptz
        };
        Insert: {
          id?: string;
          temperament: TemperamentType;
          message: string;
          mood?: string;
          time_of_day?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          temperament?: TemperamentType;
          message?: string;
          mood?: string;
          time_of_day?: string;
          created_at?: string;
        };
      };
      personality_quotes: {
        Row: {
          id: string; // uuid
          temperament: TemperamentType;
          quote: string;
          author: string;
          category: string;
          mood: string;
          time_of_day: string;
          priority: number;
          created_at: string; // timestamptz
        };
        Insert: {
          id?: string;
          temperament: TemperamentType;
          quote: string;
          author?: string;
          category?: string;
          mood?: string;
          time_of_day?: string;
          priority?: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          temperament?: TemperamentType;
          quote?: string;
          author?: string;
          category?: string;
          mood?: string;
          time_of_day?: string;
          priority?: number;
          created_at?: string;
        };
      };
      personality_tips: {
        Row: {
          id: string; // uuid
          temperament: TemperamentType;
          tip: string;
          category: string;
          mood: string;
          time_of_day: string;
          priority: number;
          created_at: string; // timestamptz
        };
        Insert: {
          id?: string;
          temperament: TemperamentType;
          tip: string;
          category?: string;
          mood?: string;
          time_of_day?: string;
          priority?: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          temperament?: TemperamentType;
          tip?: string;
          category?: string;
          mood?: string;
          time_of_day?: string;
          priority?: number;
          created_at?: string;
        };
      };
      traits: {
        Row: {
          id: string; // uuid
          temperament: TemperamentType;
          trait_name: string;
          description: string | null;
          priority: number;
          created_at: string; // timestamptz
        };
        Insert: {
          id?: string;
          temperament: TemperamentType;
          trait_name: string;
          description?: string | null;
          priority?: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          temperament?: TemperamentType;
          trait_name?: string;
          description?: string | null;
          priority?: number;
          created_at?: string;
        };
      };
      love_tank_categories: {
        Row: {
          id: string; // uuid
          name: string;
          description: string | null;
          temperament: TemperamentType | 'universal';
          icon: string;
          priority: number;
          created_at: string; // timestamptz
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          temperament: TemperamentType | 'universal';
          icon?: string;
          priority?: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          temperament?: TemperamentType | 'universal';
          icon?: string;
          priority?: number;
          created_at?: string;
        };
      };
      personal_reminders: {
        Row: {
          id: string; // uuid
          user_id: string; // uuid
          title: string;
          message: string;
          category: string; // 'self-care' | 'growth' | 'health' | 'creativity' | 'goals'
          temperament: TemperamentType | 'universal';
          frequency: string; // 'daily' | 'weekly' | 'monthly'
          is_active: boolean;
          last_shown_at: string | null; // timestamptz
          created_at: string; // timestamptz
        };
        Insert: {
          id?: string;
          user_id: string;
          title: string;
          message: string;
          category: string;
          temperament: TemperamentType | 'universal';
          frequency?: string;
          is_active?: boolean;
          last_shown_at?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          title?: string;
          message?: string;
          category?: string;
          temperament?: TemperamentType | 'universal';
          frequency?: string;
          is_active?: boolean;
          last_shown_at?: string | null;
          created_at?: string;
        };
      };
      circle_reminders: {
        Row: {
          id: string; // uuid
          user_id: string; // uuid
          contact_id: string | null; // uuid, nullable
          title: string;
          message: string;
          category: string; // 'connection' | 'appreciation' | 'support' | 'quality-time'
          temperament: TemperamentType | 'universal';
          frequency: string; // 'daily' | 'weekly' | 'monthly'
          is_active: boolean;
          last_shown_at: string | null; // timestamptz
          created_at: string; // timestamptz
        };
        Insert: {
          id?: string;
          user_id: string;
          contact_id?: string | null;
          title: string;
          message: string;
          category: string;
          temperament: TemperamentType | 'universal';
          frequency?: string;
          is_active?: boolean;
          last_shown_at?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          contact_id?: string | null;
          title?: string;
          message?: string;
          category?: string;
          temperament?: TemperamentType | 'universal';
          frequency?: string;
          is_active?: boolean;
          last_shown_at?: string | null;
          created_at?: string;
        };
      };
      comparison_history: {
        Row: {
          id: string; // uuid
          user_id: string; // uuid
          comparison_data: any; // jsonb
          created_at: string; // timestamptz
        };
        Insert: {
          id?: string;
          user_id: string;
          comparison_data: any;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          comparison_data?: any;
          created_at?: string;
        };
      };
    };
  };
}

// Type aliases for easier use
export type UserProfile = Database['public']['Tables']['user_profiles']['Row'];
export type UserProfileInsert =
  Database['public']['Tables']['user_profiles']['Insert'];
export type UserProfileUpdate =
  Database['public']['Tables']['user_profiles']['Update'];

export type PersonalityProfile =
  Database['public']['Tables']['personality_profiles']['Row'];
export type PersonalityProfileInsert =
  Database['public']['Tables']['personality_profiles']['Insert'];
export type PersonalityProfileUpdate =
  Database['public']['Tables']['personality_profiles']['Update'];

export type MoodEntry = Database['public']['Tables']['mood_entries']['Row'];
export type MoodEntryInsert =
  Database['public']['Tables']['mood_entries']['Insert'];
export type MoodEntryUpdate =
  Database['public']['Tables']['mood_entries']['Update'];

export type DailyInsight =
  Database['public']['Tables']['daily_insights']['Row'];
export type DailyInsightInsert =
  Database['public']['Tables']['daily_insights']['Insert'];
export type DailyInsightUpdate =
  Database['public']['Tables']['daily_insights']['Update'];

export type CircleContact =
  Database['public']['Tables']['circle_contacts']['Row'];
export type CircleContactInsert =
  Database['public']['Tables']['circle_contacts']['Insert'];
export type CircleContactUpdate =
  Database['public']['Tables']['circle_contacts']['Update'];

export type AssessmentResponse =
  Database['public']['Tables']['assessment_responses']['Row'];
export type AssessmentResponseInsert =
  Database['public']['Tables']['assessment_responses']['Insert'];
export type AssessmentResponseUpdate =
  Database['public']['Tables']['assessment_responses']['Update'];

// Additional table types from other migrations
export type Subgreeting = Database['public']['Tables']['subgreetings']['Row'];
export type SubgreetingInsert =
  Database['public']['Tables']['subgreetings']['Insert'];
export type SubgreetingUpdate =
  Database['public']['Tables']['subgreetings']['Update'];

export type PersonalityQuote =
  Database['public']['Tables']['personality_quotes']['Row'];
export type PersonalityQuoteInsert =
  Database['public']['Tables']['personality_quotes']['Insert'];
export type PersonalityQuoteUpdate =
  Database['public']['Tables']['personality_quotes']['Update'];

export type PersonalityTip =
  Database['public']['Tables']['personality_tips']['Row'];
export type PersonalityTipInsert =
  Database['public']['Tables']['personality_tips']['Insert'];
export type PersonalityTipUpdate =
  Database['public']['Tables']['personality_tips']['Update'];

export type Trait = Database['public']['Tables']['traits']['Row'];
export type TraitInsert = Database['public']['Tables']['traits']['Insert'];
export type TraitUpdate = Database['public']['Tables']['traits']['Update'];

export type LoveTankCategory =
  Database['public']['Tables']['love_tank_categories']['Row'];
export type LoveTankCategoryInsert =
  Database['public']['Tables']['love_tank_categories']['Insert'];
export type LoveTankCategoryUpdate =
  Database['public']['Tables']['love_tank_categories']['Update'];

export type PersonalReminder =
  Database['public']['Tables']['personal_reminders']['Row'];
export type PersonalReminderInsert =
  Database['public']['Tables']['personal_reminders']['Insert'];
export type PersonalReminderUpdate =
  Database['public']['Tables']['personal_reminders']['Update'];

export type CircleReminder =
  Database['public']['Tables']['circle_reminders']['Row'];
export type CircleReminderInsert =
  Database['public']['Tables']['circle_reminders']['Insert'];
export type CircleReminderUpdate =
  Database['public']['Tables']['circle_reminders']['Update'];

export type ComparisonHistory =
  Database['public']['Tables']['comparison_history']['Row'];
export type ComparisonHistoryInsert =
  Database['public']['Tables']['comparison_history']['Insert'];
export type ComparisonHistoryUpdate =
  Database['public']['Tables']['comparison_history']['Update'];
