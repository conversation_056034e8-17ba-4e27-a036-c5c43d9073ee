/*
  # Create saved insights table

  1. New Tables
    - `saved_insights`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to user_profiles)
      - `insight_type` (text, constraint: insight/tip/quote)
      - `content` (text)
      - `temperament` (text, constraint: choleric/sanguine/melancholic/phlegmatic)
      - `saved_at` (timestamp)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on `saved_insights` table
    - Add policy for users to manage their own saved insights

  3. Performance
    - Add indexes for user_id, insight_type, and temperament
*/

-- Create saved_insights table for users to save their favorite insights
CREATE TABLE IF NOT EXISTS saved_insights (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  insight_type text NOT NULL,
  content text NOT NULL,
  temperament text NOT NULL,
  saved_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now()
);

-- Add constraints
ALTER TABLE saved_insights 
ADD CONSTRAINT saved_insights_temperament_check 
CHECK (temperament = ANY (ARRAY['choleric'::text, 'sanguine'::text, 'melancholic'::text, 'phlegmatic'::text]));

ALTER TABLE saved_insights 
ADD CONSTRAINT saved_insights_type_check 
CHECK (insight_type = ANY (ARRAY['insight'::text, 'tip'::text, 'quote'::text]));

-- Add foreign key constraint to user_profiles (not users)
ALTER TABLE saved_insights 
ADD CONSTRAINT saved_insights_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES user_profiles(id) ON DELETE CASCADE;

-- Enable RLS
ALTER TABLE saved_insights ENABLE ROW LEVEL SECURITY;

-- Create policies for saved insights
CREATE POLICY "Users can manage own saved insights"
  ON saved_insights
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS saved_insights_user_id_idx ON saved_insights(user_id);
CREATE INDEX IF NOT EXISTS saved_insights_type_idx ON saved_insights(insight_type);
CREATE INDEX IF NOT EXISTS saved_insights_temperament_idx ON saved_insights(temperament);