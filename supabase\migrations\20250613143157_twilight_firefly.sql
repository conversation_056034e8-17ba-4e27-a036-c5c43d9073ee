/*
  # Fix user profile creation trigger

  1. Database Functions
    - Create or replace `handle_new_user` function to automatically create user profiles
    - Fun<PERSON> creates a profile entry when a new user is inserted into auth.users

  2. Triggers  
    - Create trigger to call `handle_new_user` after user insertion
    - Ensures user_profiles entry exists before client-side code tries to access it

  3. Security
    - Maintains existing RLS policies on user_profiles table
*/

-- Create or replace the function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  INSERT INTO public.user_profiles (
    id,
    full_name,
    email,
    email_verified,
    has_completed_assessment
  )
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.email, ''),
    COALESCE(NEW.email_confirmed_at IS NOT NULL, false),
    false
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop the trigger if it exists and recreate it
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create the trigger to automatically create user profiles
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON public.user_profiles TO postgres, anon, authenticated, service_role;