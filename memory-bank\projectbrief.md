# Harmona Project Brief

## Project Overview

**Name**: Harmona - Personality & Relationship App  
**Version**: 1.0.0  
**Date**: June 15, 2025

## Purpose

Harmona is a mobile application designed to help users understand their personality temperaments and improve their relationships through this self-awareness. The app leverages the classic four temperaments theory (Choleric, Sanguine, Melancholic, Phlegmatic) to provide insights, mood tracking, and relationship management features.

## Core Requirements

1. **Personality Assessment**: Provide users with a comprehensive assessment to determine their primary and secondary temperaments.
2. **Personal Insights**: Deliver daily insights, tips, and quotes tailored to the user's personality profile.
3. **Mood Tracking**: Allow users to log and track their emotional states over time.
4. **Relationship Circle**: Enable users to manage a list of important relationships and understand compatibility.
5. **Secure Authentication**: Implement secure user registration, login, and email verification.
6. **Intuitive Navigation**: Provide a seamless and intuitive user experience through a tab-based navigation structure.

## Target Users

- Individuals interested in personal growth and self-awareness
- People seeking to improve their relationships through personality understanding
- Users who enjoy tracking patterns in their moods and behaviors
- Those looking for personalized daily insights and growth recommendations

## Features

### MVP Features (v1.0)

1. **User Authentication**

   - Email-based registration and login
   - Email verification process
   - Password reset functionality
   - Demo mode for users without accounts

2. **Personality Assessment**

   - Questionnaire to determine temperament profile
   - Results visualization with primary and secondary temperaments
   - Explanation of temperament characteristics

3. **Home Dashboard**

   - Daily insights based on personality profile
   - Personalized greeting and recommendation
   - Current mood display

4. **Mood Tracking**

   - Daily mood logging with emoji selection
   - Optional mood description entry
   - Historical mood data visualization

5. **Relationship Circle**

   - Add and categorize contacts (family, friends, work, other)
   - Assign/input temperaments for contacts
   - View relationship compatibility insights

6. **Comparison Tool**

   - Compare user's temperament with contacts
   - Generate compatibility insights and tips
   - Provide communication recommendations

7. **AI-Assisted Conversation**

   - Get temperament-specific advice
   - Receive suggested responses for specific situations
   - Explore personality-related questions

8. **Settings & Profile Management**
   - View and edit profile information
   - Manage notification preferences
   - Access help and support resources

### Future Features (Post v1.0)

1. **Love Tank Tracker**

   - Track relationship satisfaction across categories
   - Receive recommendations to improve relationships

2. **Personal Reminders**

   - Scheduled reminders based on personality type
   - Self-care and personal growth prompts

3. **Circle Reminders**

   - Scheduled reminders for maintaining relationships
   - Birthday and special occasion notifications

4. **Advanced Analytics**
   - Correlations between mood and activities
   - Personality trend analysis
   - Relationship health tracking

## Technology Scope

- **Frontend**: React Native via Expo framework
- **Backend**: Supabase (Authentication, Database, Storage)
- **AI Integration**: OpenRouter API for AI-generated content
- **Analytics**: To be determined

## Constraints & Limitations

- Initial release targeted for iOS and Android mobile platforms
- Web version will be limited in functionality
- Offline functionality will be limited
- AI features dependent on API availability and credits

## Success Criteria

1. Users complete the personality assessment and receive accurate results
2. Daily engagement with insights and mood tracking features
3. Addition of multiple contacts to relationship circle
4. Positive user feedback on the accuracy of personality insights
5. Repeated app usage over extended periods

## Timeline

- **Initial Development**: June 2025
- **Beta Testing**: July 2025
- **Public Release**: August 2025

This project brief serves as the foundation for all development work on the Harmona app and should be referenced when making key decisions about features, implementation approaches, and priorities.
