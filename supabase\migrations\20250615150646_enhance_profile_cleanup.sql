-- Migration to enhance profile cleanup functionality
-- This creates a more robust transaction-based stored procedure that ensures
-- all profiles are deleted before inserting a new one

-- Drop the existing function if it exists
DROP FUNCTION IF EXISTS force_delete_personality_profiles;

-- Create a more robust transaction-based procedure with additional verification
CREATE OR REPLACE FUNCTION force_delete_personality_profiles_transaction(user_id_param UUID) 
RETURNS boolean AS $$
DECLARE
    remaining_count INTEGER;
    max_attempts INTEGER := 5;
    current_attempt INTEGER := 0;
    success BOOLEAN := FALSE;
    profile_rec RECORD; -- Explicit declaration of the record variable
BEGIN
    -- Begin a transaction with the highest isolation level
    SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
    
    -- Loop for multiple attempts
    WHILE current_attempt < max_attempts AND NOT success LOOP
        current_attempt := current_attempt + 1;
        
        -- Clear any prior transaction state
        SAVEPOINT cleanup_attempt;
        
        BEGIN
            -- First attempt: Delete via foreign key cascades
            DELETE FROM assessment_responses
            WHERE user_id = user_id_param;
            
            -- Next, delete all personality profiles directly
            DELETE FROM personality_profiles
            WHERE user_id = user_id_param;
            
            -- Verify deletion was successful
            SELECT COUNT(*) INTO remaining_count
            FROM personality_profiles
            WHERE user_id = user_id_param;
            
            IF remaining_count = 0 THEN
                success := TRUE;
            ELSE
                -- If deletion didn't work, try again with a different approach
                -- This bypasses potential foreign key or trigger issues
                EXECUTE 'DELETE FROM personality_profiles WHERE user_id = $1'
                USING user_id_param;
                
                -- Check again
                SELECT COUNT(*) INTO remaining_count
                FROM personality_profiles
                WHERE user_id = user_id_param;
                
                IF remaining_count = 0 THEN
                    success := TRUE;
                ELSE
                    -- If we still have records, try one more time with direct ID deletion
                    FOR profile_rec IN 
                        SELECT id FROM personality_profiles WHERE user_id = user_id_param
                    LOOP
                        EXECUTE 'DELETE FROM personality_profiles WHERE id = $1'
                        USING profile_rec.id;
                    END LOOP;
                    
                    -- Final verification
                    SELECT COUNT(*) INTO remaining_count
                    FROM personality_profiles
                    WHERE user_id = user_id_param;
                    
                    IF remaining_count = 0 THEN
                        success := TRUE;
                    END IF;
                END IF;
            END IF;
            
            -- If we succeeded, commit; otherwise roll back to savepoint and try again
            IF success THEN
                -- Do nothing, will be committed at the end
            ELSE
                ROLLBACK TO cleanup_attempt;
            END IF;
        EXCEPTION WHEN OTHERS THEN
            -- On error, roll back to savepoint and try a different approach
            ROLLBACK TO cleanup_attempt;
        END;
    END LOOP;
    
    -- Return success status
    RETURN success;
END;
$$ LANGUAGE plpgsql;

-- Create a simpler function that matches the original name but uses the new transaction-based one
CREATE OR REPLACE FUNCTION force_delete_personality_profiles(user_id_param UUID) 
RETURNS void AS $$
DECLARE
    success BOOLEAN;
BEGIN
    -- Call the transaction-based function
    success := force_delete_personality_profiles_transaction(user_id_param);
    
    -- If unsuccessful, raise exception
    IF NOT success THEN
        RAISE EXCEPTION 'Failed to delete all profiles for user % after multiple attempts', user_id_param;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Add comments to the function for documentation
COMMENT ON FUNCTION force_delete_personality_profiles(UUID) IS 
'Forcefully deletes all personality profiles for a given user ID using a robust transaction-based approach.
Used to ensure clean state before creating a new profile.';

COMMENT ON FUNCTION force_delete_personality_profiles_transaction(UUID) IS
'Transaction-based implementation that attempts multiple cleanup strategies.
Returns a boolean indicating success or failure.';
