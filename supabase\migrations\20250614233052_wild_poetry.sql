/*
  # Love Tank and Reminders System

  1. New Tables
    - `love_tanks` - Track emotional needs and fulfillment levels
    - `love_tank_categories` - Different types of emotional needs
    - `personal_reminders` - Self-care and personal growth reminders
    - `circle_reminders` - Reminders about relationships and connections
    - `reminder_schedules` - When and how often reminders should appear

  2. Security
    - Enable RLS on all new tables
    - Add policies for authenticated users to manage their own data

  3. Features
    - Personality-based love tank categories
    - Dynamic reminder generation based on temperament
    - Self-care and relationship care reminders
    - Customizable reminder frequencies
*/

-- Create love tank categories table
CREATE TABLE IF NOT EXISTS love_tank_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  temperament text NOT NULL,
  icon text DEFAULT '❤️',
  priority integer DEFAULT 1,
  created_at timestamptz DEFAULT now()
);

-- Create love tanks table to track user's emotional needs
CREATE TABLE IF NOT EXISTS love_tanks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  category_id uuid NOT NULL,
  current_level integer DEFAULT 50 CHECK (current_level >= 0 AND current_level <= 100),
  target_level integer DEFAULT 80 CHECK (target_level >= 0 AND target_level <= 100),
  last_filled_at timestamptz,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create personal reminders table
CREATE TABLE IF NOT EXISTS personal_reminders (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  title text NOT NULL,
  message text NOT NULL,
  category text NOT NULL, -- 'self-care', 'growth', 'health', 'creativity', 'goals'
  temperament text NOT NULL,
  frequency text DEFAULT 'daily', -- 'daily', 'weekly', 'monthly'
  is_active boolean DEFAULT true,
  last_shown_at timestamptz,
  created_at timestamptz DEFAULT now()
);

-- Create circle reminders table
CREATE TABLE IF NOT EXISTS circle_reminders (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  contact_id uuid, -- References circle_contacts, nullable for general reminders
  title text NOT NULL,
  message text NOT NULL,
  category text NOT NULL, -- 'connection', 'appreciation', 'support', 'quality-time'
  temperament text NOT NULL,
  frequency text DEFAULT 'weekly',
  is_active boolean DEFAULT true,
  last_shown_at timestamptz,
  created_at timestamptz DEFAULT now()
);

-- Add constraints
ALTER TABLE love_tank_categories 
ADD CONSTRAINT love_tank_categories_temperament_check 
CHECK (temperament = ANY (ARRAY['choleric'::text, 'sanguine'::text, 'melancholic'::text, 'phlegmatic'::text, 'universal'::text]));

ALTER TABLE love_tank_categories 
ADD CONSTRAINT love_tank_categories_priority_check 
CHECK (priority >= 1 AND priority <= 10);

ALTER TABLE personal_reminders 
ADD CONSTRAINT personal_reminders_temperament_check 
CHECK (temperament = ANY (ARRAY['choleric'::text, 'sanguine'::text, 'melancholic'::text, 'phlegmatic'::text, 'universal'::text]));

ALTER TABLE personal_reminders 
ADD CONSTRAINT personal_reminders_category_check 
CHECK (category = ANY (ARRAY['self-care'::text, 'growth'::text, 'health'::text, 'creativity'::text, 'goals'::text, 'mindfulness'::text]));

ALTER TABLE personal_reminders 
ADD CONSTRAINT personal_reminders_frequency_check 
CHECK (frequency = ANY (ARRAY['daily'::text, 'weekly'::text, 'monthly'::text]));

ALTER TABLE circle_reminders 
ADD CONSTRAINT circle_reminders_temperament_check 
CHECK (temperament = ANY (ARRAY['choleric'::text, 'sanguine'::text, 'melancholic'::text, 'phlegmatic'::text, 'universal'::text]));

ALTER TABLE circle_reminders 
ADD CONSTRAINT circle_reminders_category_check 
CHECK (category = ANY (ARRAY['connection'::text, 'appreciation'::text, 'support'::text, 'quality-time'::text, 'communication'::text]));

ALTER TABLE circle_reminders 
ADD CONSTRAINT circle_reminders_frequency_check 
CHECK (frequency = ANY (ARRAY['daily'::text, 'weekly'::text, 'monthly'::text]));

-- Add foreign key constraints
ALTER TABLE love_tanks 
ADD CONSTRAINT love_tanks_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES user_profiles(id) ON DELETE CASCADE;

ALTER TABLE love_tanks 
ADD CONSTRAINT love_tanks_category_id_fkey 
FOREIGN KEY (category_id) REFERENCES love_tank_categories(id) ON DELETE CASCADE;

ALTER TABLE personal_reminders 
ADD CONSTRAINT personal_reminders_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES user_profiles(id) ON DELETE CASCADE;

ALTER TABLE circle_reminders 
ADD CONSTRAINT circle_reminders_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES user_profiles(id) ON DELETE CASCADE;

ALTER TABLE circle_reminders 
ADD CONSTRAINT circle_reminders_contact_id_fkey 
FOREIGN KEY (contact_id) REFERENCES circle_contacts(id) ON DELETE CASCADE;

-- Enable RLS
ALTER TABLE love_tank_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE love_tanks ENABLE ROW LEVEL SECURITY;
ALTER TABLE personal_reminders ENABLE ROW LEVEL SECURITY;
ALTER TABLE circle_reminders ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Anyone can read love tank categories"
  ON love_tank_categories
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can manage own love tanks"
  ON love_tanks
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can manage own personal reminders"
  ON personal_reminders
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can manage own circle reminders"
  ON circle_reminders
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Insert love tank categories for each temperament
INSERT INTO love_tank_categories (name, description, temperament, icon, priority) VALUES
-- Choleric love tank categories
('Achievement Recognition', 'Acknowledgment of accomplishments and progress toward goals', 'choleric', '🏆', 1),
('Autonomy & Control', 'Freedom to make decisions and lead initiatives', 'choleric', '⚡', 2),
('Challenge & Growth', 'Opportunities to overcome obstacles and develop skills', 'choleric', '🎯', 3),
('Respect & Authority', 'Recognition of expertise and leadership capabilities', 'choleric', '👑', 4),

-- Sanguine love tank categories
('Social Connection', 'Meaningful interactions and time with loved ones', 'sanguine', '🤝', 1),
('Appreciation & Praise', 'Verbal affirmation and recognition of positive qualities', 'sanguine', '🌟', 2),
('Fun & Adventure', 'Exciting experiences and joyful activities', 'sanguine', '🎉', 3),
('Emotional Expression', 'Freedom to share feelings and be authentically expressive', 'sanguine', '💫', 4),

-- Melancholic love tank categories
('Deep Understanding', 'Being truly seen and understood on a profound level', 'melancholic', '🔍', 1),
('Creative Expression', 'Opportunities to create and share artistic or meaningful work', 'melancholic', '🎨', 2),
('Quality Time', 'Uninterrupted, meaningful time for reflection and connection', 'melancholic', '⏰', 3),
('Emotional Safety', 'Safe spaces to process feelings without judgment', 'melancholic', '🛡️', 4),

-- Phlegmatic love tank categories
('Peace & Harmony', 'Calm environments free from conflict and stress', 'phlegmatic', '☮️', 1),
('Gentle Support', 'Quiet encouragement and steady, reliable presence', 'phlegmatic', '🤗', 2),
('Acceptance & Belonging', 'Unconditional acceptance and sense of belonging', 'phlegmatic', '🏠', 3),
('Comfortable Routine', 'Predictable, comfortable patterns and traditions', 'phlegmatic', '🔄', 4),

-- Universal categories that apply to all temperaments
('Physical Affection', 'Hugs, touch, and physical closeness', 'universal', '🤗', 1),
('Acts of Service', 'Helpful actions that make life easier', 'universal', '🛠️', 2),
('Quality Conversation', 'Deep, meaningful dialogue and active listening', 'universal', '💬', 3),
('Thoughtful Gifts', 'Meaningful tokens of love and appreciation', 'universal', '🎁', 4);

-- Insert personality-based personal reminders
INSERT INTO personal_reminders (user_id, title, message, category, temperament, frequency) 
SELECT 
  up.id,
  'Goal Progress Check',
  'Take 10 minutes to review your goals and celebrate your progress. Your drive for achievement deserves recognition.',
  'goals',
  'choleric',
  'weekly'
FROM user_profiles up
JOIN personality_profiles pp ON up.id = pp.user_id
WHERE pp.dominant_temperament = 'choleric';

INSERT INTO personal_reminders (user_id, title, message, category, temperament, frequency) 
SELECT 
  up.id,
  'Social Energy Boost',
  'Reach out to a friend or plan a fun activity. Your social connections fuel your happiness and energy.',
  'self-care',
  'sanguine',
  'daily'
FROM user_profiles up
JOIN personality_profiles pp ON up.id = pp.user_id
WHERE pp.dominant_temperament = 'sanguine';

INSERT INTO personal_reminders (user_id, title, message, category, temperament, frequency) 
SELECT 
  up.id,
  'Creative Expression Time',
  'Set aside time for creative work or deep thinking. Your artistic soul needs space to flourish.',
  'creativity',
  'melancholic',
  'daily'
FROM user_profiles up
JOIN personality_profiles pp ON up.id = pp.user_id
WHERE pp.dominant_temperament = 'melancholic';

INSERT INTO personal_reminders (user_id, title, message, category, temperament, frequency) 
SELECT 
  up.id,
  'Peaceful Moment',
  'Take a few minutes to find calm and center yourself. Your peaceful nature is a gift to nurture.',
  'mindfulness',
  'phlegmatic',
  'daily'
FROM user_profiles up
JOIN personality_profiles pp ON up.id = pp.user_id
WHERE pp.dominant_temperament = 'phlegmatic';

-- Insert circle reminders for relationship care
INSERT INTO circle_reminders (user_id, contact_id, title, message, category, temperament, frequency)
SELECT 
  up.id,
  NULL, -- General reminder, not specific to one contact
  'Leadership Check-in',
  'Reach out to someone in your circle who might benefit from your guidance and support.',
  'support',
  'choleric',
  'weekly'
FROM user_profiles up
JOIN personality_profiles pp ON up.id = pp.user_id
WHERE pp.dominant_temperament = 'choleric';

INSERT INTO circle_reminders (user_id, contact_id, title, message, category, temperament, frequency)
SELECT 
  up.id,
  NULL,
  'Spread Joy',
  'Send an encouraging message or share something positive with someone in your circle.',
  'appreciation',
  'sanguine',
  'daily'
FROM user_profiles up
JOIN personality_profiles pp ON up.id = pp.user_id
WHERE pp.dominant_temperament = 'sanguine';

INSERT INTO circle_reminders (user_id, contact_id, title, message, category, temperament, frequency)
SELECT 
  up.id,
  NULL,
  'Deep Connection',
  'Have a meaningful conversation with someone important to you. Quality over quantity.',
  'connection',
  'melancholic',
  'weekly'
FROM user_profiles up
JOIN personality_profiles pp ON up.id = pp.user_id
WHERE pp.dominant_temperament = 'melancholic';

INSERT INTO circle_reminders (user_id, contact_id, title, message, category, temperament, frequency)
SELECT 
  up.id,
  NULL,
  'Gentle Support',
  'Check in with someone who might need your calm, steady presence today.',
  'support',
  'phlegmatic',
  'weekly'
FROM user_profiles up
JOIN personality_profiles pp ON up.id = pp.user_id
WHERE pp.dominant_temperament = 'phlegmatic';

-- Create function to get active reminders for a user
CREATE OR REPLACE FUNCTION get_active_reminders(user_uuid uuid)
RETURNS TABLE(
  id uuid,
  title text,
  message text,
  category text,
  reminder_type text,
  contact_name text,
  frequency text,
  last_shown_at timestamptz
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Get personal reminders
  RETURN QUERY
  SELECT 
    pr.id,
    pr.title,
    pr.message,
    pr.category,
    'personal'::text as reminder_type,
    NULL::text as contact_name,
    pr.frequency,
    pr.last_shown_at
  FROM personal_reminders pr
  WHERE pr.user_id = user_uuid 
    AND pr.is_active = true
    AND (
      pr.last_shown_at IS NULL OR
      (pr.frequency = 'daily' AND pr.last_shown_at < NOW() - INTERVAL '1 day') OR
      (pr.frequency = 'weekly' AND pr.last_shown_at < NOW() - INTERVAL '1 week') OR
      (pr.frequency = 'monthly' AND pr.last_shown_at < NOW() - INTERVAL '1 month')
    )
  ORDER BY RANDOM()
  LIMIT 2;

  -- Get circle reminders
  RETURN QUERY
  SELECT 
    cr.id,
    cr.title,
    cr.message,
    cr.category,
    'circle'::text as reminder_type,
    cc.name as contact_name,
    cr.frequency,
    cr.last_shown_at
  FROM circle_reminders cr
  LEFT JOIN circle_contacts cc ON cr.contact_id = cc.id
  WHERE cr.user_id = user_uuid 
    AND cr.is_active = true
    AND (
      cr.last_shown_at IS NULL OR
      (cr.frequency = 'daily' AND cr.last_shown_at < NOW() - INTERVAL '1 day') OR
      (cr.frequency = 'weekly' AND cr.last_shown_at < NOW() - INTERVAL '1 week') OR
      (cr.frequency = 'monthly' AND cr.last_shown_at < NOW() - INTERVAL '1 month')
    )
  ORDER BY RANDOM()
  LIMIT 2;
END;
$$;

-- Create function to mark reminder as shown
CREATE OR REPLACE FUNCTION mark_reminder_shown(
  reminder_id uuid,
  reminder_type text
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF reminder_type = 'personal' THEN
    UPDATE personal_reminders 
    SET last_shown_at = NOW()
    WHERE id = reminder_id AND user_id = auth.uid();
  ELSIF reminder_type = 'circle' THEN
    UPDATE circle_reminders 
    SET last_shown_at = NOW()
    WHERE id = reminder_id AND user_id = auth.uid();
  END IF;
  
  RETURN FOUND;
END;
$$;

-- Create function to update love tank level
CREATE OR REPLACE FUNCTION update_love_tank_level(
  tank_id uuid,
  new_level integer
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE love_tanks 
  SET 
    current_level = new_level,
    last_filled_at = CASE WHEN new_level > current_level THEN NOW() ELSE last_filled_at END,
    updated_at = NOW()
  WHERE id = tank_id AND user_id = auth.uid();
  
  RETURN FOUND;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_active_reminders(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION mark_reminder_shown(uuid, text) TO authenticated;
GRANT EXECUTE ON FUNCTION update_love_tank_level(uuid, integer) TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS love_tanks_user_id_idx ON love_tanks(user_id);
CREATE INDEX IF NOT EXISTS love_tanks_category_id_idx ON love_tanks(category_id);
CREATE INDEX IF NOT EXISTS personal_reminders_user_id_idx ON personal_reminders(user_id);
CREATE INDEX IF NOT EXISTS personal_reminders_active_idx ON personal_reminders(user_id, is_active);
CREATE INDEX IF NOT EXISTS circle_reminders_user_id_idx ON circle_reminders(user_id);
CREATE INDEX IF NOT EXISTS circle_reminders_active_idx ON circle_reminders(user_id, is_active);