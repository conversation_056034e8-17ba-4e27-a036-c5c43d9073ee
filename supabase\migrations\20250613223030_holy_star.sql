/*
  # Update Subgreetings to Use Emoji-Based Moods

  1. Schema Changes
    - Update mood constraint to use emojis instead of text values
    - Clear existing data and insert new emoji-based subgreetings
    
  2. New Data Structure
    - 120 new subgreetings (30 per temperament)
    - Emoji moods: 😄, 🙂, 😐, 😕, 😢
    - Mood-specific content for each temperament
    
  3. Helper Functions
    - Function to get user's latest mood for personalized greetings
*/

-- Step 1: Clear existing data first to avoid constraint conflicts
DELETE FROM subgreetings;

-- Step 2: Drop the old constraint
ALTER TABLE subgreetings DROP CONSTRAINT IF EXISTS subgreetings_mood_check;

-- Step 3: Add the new constraint with emoji support
ALTER TABLE subgreetings 
ADD CONSTRAINT subgreetings_mood_check 
CHECK (mood = ANY (ARRAY['😄'::text, '🙂'::text, '😐'::text, '😕'::text, '😢'::text, 'neutral'::text]));

-- Step 4: Insert new subgreetings for Choleric temperament with emoji moods
INSERT INTO subgreetings (temperament, message, mood, time_of_day) VALUES
-- Very Happy (😄) - Energetic and triumphant
('choleric', 'absolutely crushing your goals today', '😄', 'morning'),
('choleric', 'on fire and unstoppable', '😄', 'any'),
('choleric', 'dominating every challenge that comes your way', '😄', 'afternoon'),
('choleric', 'celebrating another victory', '😄', 'evening'),
('choleric', 'achieving greatness with unstoppable energy', '😄', 'any'),
('choleric', 'leading with confidence and power', '😄', 'morning'),
('choleric', 'conquering the day with fierce determination', '😄', 'afternoon'),
('choleric', 'triumphant and ready for more challenges', '😄', 'evening'),

-- Happy (🙂) - Confident and driven
('choleric', 'ready to conquer your goals today', '🙂', 'morning'),
('choleric', 'in full leadership mode', '🙂', 'any'),
('choleric', 'channeling your natural determination', '🙂', 'any'),
('choleric', 'focused on achieving excellence', '🙂', 'afternoon'),
('choleric', 'embracing your decisive nature', '🙂', 'evening'),
('choleric', 'driving forward with purpose and clarity', '🙂', 'morning'),
('choleric', 'making strategic moves toward success', '🙂', 'afternoon'),
('choleric', 'ending the day with accomplished satisfaction', '🙂', 'evening'),

-- Neutral (😐) - Steady and focused
('choleric', 'maintaining your strategic focus', '😐', 'any'),
('choleric', 'staying committed to your objectives', '😐', 'afternoon'),
('choleric', 'keeping your eyes on the prize', '😐', 'any'),
('choleric', 'working steadily toward your goals', '😐', 'morning'),
('choleric', 'maintaining professional composure', '😐', 'afternoon'),
('choleric', 'staying disciplined and on track', '😐', 'evening'),

-- Sad (😕) - Determined despite challenges
('choleric', 'pushing through with unwavering resolve', '😕', 'any'),
('choleric', 'turning setbacks into comebacks', '😕', 'afternoon'),
('choleric', 'finding strength in adversity', '😕', 'evening'),
('choleric', 'refusing to let challenges define you', '😕', 'morning'),
('choleric', 'channeling frustration into motivation', '😕', 'afternoon'),
('choleric', 'preparing for tomorrow''s victories', '😕', 'evening'),

-- Very Sad (😢) - Resilient and rebuilding
('choleric', 'rebuilding with even greater determination', '😢', 'any'),
('choleric', 'using this moment to fuel your comeback', '😢', 'evening'),
('choleric', 'finding the warrior within during tough times', '😢', 'any'),
('choleric', 'transforming pain into unstoppable drive', '😢', 'evening');

-- Insert new subgreetings for Sanguine temperament with emoji moods
INSERT INTO subgreetings (temperament, message, mood, time_of_day) VALUES
-- Very Happy (😄) - Extremely social and joyful
('sanguine', 'absolutely radiating pure joy and excitement', '😄', 'morning'),
('sanguine', 'spreading infectious happiness everywhere', '😄', 'any'),
('sanguine', 'lighting up the world with your energy', '😄', 'afternoon'),
('sanguine', 'celebrating life in the most beautiful way', '😄', 'evening'),
('sanguine', 'bouncing with irrepressible enthusiasm', '😄', 'any'),
('sanguine', 'making every moment a celebration', '😄', 'morning'),
('sanguine', 'turning ordinary moments into magic', '😄', 'afternoon'),
('sanguine', 'ending the day with pure bliss', '😄', 'evening'),

-- Happy (🙂) - Optimistic and social
('sanguine', 'radiating positive energy and enthusiasm', '🙂', 'morning'),
('sanguine', 'bringing joy to everyone around you', '🙂', 'any'),
('sanguine', 'spreading optimism wherever you go', '🙂', 'any'),
('sanguine', 'connecting hearts and building friendships', '🙂', 'afternoon'),
('sanguine', 'inspiring others with your zest for life', '🙂', 'evening'),
('sanguine', 'starting the day with bright possibilities', '🙂', 'morning'),
('sanguine', 'sharing smiles and creating connections', '🙂', 'afternoon'),
('sanguine', 'wrapping up with gratitude and warmth', '🙂', 'evening'),

-- Neutral (😐) - Balanced but still social
('sanguine', 'finding your social rhythm today', '😐', 'any'),
('sanguine', 'staying connected with those who matter', '😐', 'afternoon'),
('sanguine', 'maintaining your natural warmth', '😐', 'any'),
('sanguine', 'keeping things light and friendly', '😐', 'morning'),
('sanguine', 'balancing energy with calm presence', '😐', 'afternoon'),
('sanguine', 'staying open to new connections', '😐', 'evening'),

-- Sad (😕) - Seeking comfort through connection
('sanguine', 'finding comfort in meaningful connections', '😕', 'any'),
('sanguine', 'letting friends lift your spirits', '😕', 'afternoon'),
('sanguine', 'remembering that brighter days are ahead', '😕', 'evening'),
('sanguine', 'seeking the silver lining in difficult moments', '😕', 'morning'),
('sanguine', 'drawing strength from your support network', '😕', 'afternoon'),
('sanguine', 'holding onto hope for tomorrow', '😕', 'evening'),

-- Very Sad (😢) - Needing support and understanding
('sanguine', 'allowing yourself to feel and heal', '😢', 'any'),
('sanguine', 'finding strength in the love around you', '😢', 'evening'),
('sanguine', 'accepting comfort from those who care', '😢', 'any'),
('sanguine', 'trusting that joy will return again', '😢', 'evening');

-- Insert new subgreetings for Melancholic temperament with emoji moods
INSERT INTO subgreetings (temperament, message, mood, time_of_day) VALUES
-- Very Happy (😄) - Creatively inspired and fulfilled
('melancholic', 'experiencing pure creative bliss', '😄', 'morning'),
('melancholic', 'feeling deeply fulfilled and inspired', '😄', 'any'),
('melancholic', 'creating something truly magnificent', '😄', 'afternoon'),
('melancholic', 'finding perfect harmony in your thoughts', '😄', 'evening'),
('melancholic', 'touched by profound beauty and meaning', '😄', 'any'),
('melancholic', 'awakening to artistic inspiration', '😄', 'morning'),
('melancholic', 'discovering deep truths and insights', '😄', 'afternoon'),
('melancholic', 'feeling the poetry of existence', '😄', 'evening'),

-- Happy (🙂) - Thoughtful and creative
('melancholic', 'diving deep into thoughtful reflection', '🙂', 'morning'),
('melancholic', 'appreciating the beauty in details', '🙂', 'any'),
('melancholic', 'channeling your analytical brilliance', '🙂', 'any'),
('melancholic', 'expressing your rich inner world', '🙂', 'afternoon'),
('melancholic', 'connecting with deeper truths and meanings', '🙂', 'evening'),
('melancholic', 'starting with quiet contemplation', '🙂', 'morning'),
('melancholic', 'finding satisfaction in meaningful work', '🙂', 'afternoon'),
('melancholic', 'reflecting on the day''s deeper lessons', '🙂', 'evening'),

-- Neutral (😐) - Contemplative and steady
('melancholic', 'processing thoughts with quiet wisdom', '😐', 'any'),
('melancholic', 'finding clarity in contemplation', '😐', 'afternoon'),
('melancholic', 'maintaining your thoughtful perspective', '😐', 'any'),
('melancholic', 'observing the world with careful attention', '😐', 'morning'),
('melancholic', 'working through ideas methodically', '😐', 'afternoon'),
('melancholic', 'settling into peaceful introspection', '😐', 'evening'),

-- Sad (😕) - Introspective and seeking meaning
('melancholic', 'finding meaning in difficult moments', '😕', 'any'),
('melancholic', 'using introspection to understand deeper', '😕', 'afternoon'),
('melancholic', 'transforming pain into profound insight', '😕', 'evening'),
('melancholic', 'seeking understanding through reflection', '😕', 'morning'),
('melancholic', 'processing emotions with gentle patience', '😕', 'afternoon'),
('melancholic', 'finding wisdom in life''s challenges', '😕', 'evening'),

-- Very Sad (😢) - Deeply feeling and processing
('melancholic', 'honoring the depth of your emotions', '😢', 'any'),
('melancholic', 'finding beauty even in sorrow', '😢', 'evening'),
('melancholic', 'allowing yourself to feel deeply and fully', '😢', 'any'),
('melancholic', 'trusting in the healing power of time', '😢', 'evening');

-- Insert new subgreetings for Phlegmatic temperament with emoji moods
INSERT INTO subgreetings (temperament, message, mood, time_of_day) VALUES
-- Very Happy (😄) - Peacefully joyful and harmonious
('phlegmatic', 'radiating peaceful joy and contentment', '😄', 'morning'),
('phlegmatic', 'spreading harmony and happiness', '😄', 'any'),
('phlegmatic', 'creating beautiful moments of serenity', '😄', 'afternoon'),
('phlegmatic', 'feeling perfectly balanced and joyful', '😄', 'evening'),
('phlegmatic', 'glowing with quiet satisfaction', '😄', 'any'),
('phlegmatic', 'beginning with gentle enthusiasm', '😄', 'morning'),
('phlegmatic', 'sharing calm joy with others', '😄', 'afternoon'),
('phlegmatic', 'ending with peaceful happiness', '😄', 'evening'),

-- Happy (🙂) - Calm and supportive
('phlegmatic', 'bringing peace and harmony to your surroundings', '🙂', 'morning'),
('phlegmatic', 'being the steady anchor others rely on', '🙂', 'any'),
('phlegmatic', 'creating a sense of comfort and stability', '🙂', 'any'),
('phlegmatic', 'nurturing relationships with gentle care', '🙂', 'afternoon'),
('phlegmatic', 'offering support with your loyal heart', '🙂', 'evening'),
('phlegmatic', 'starting with calm confidence', '🙂', 'morning'),
('phlegmatic', 'maintaining steady positive energy', '🙂', 'afternoon'),
('phlegmatic', 'closing with satisfied contentment', '🙂', 'evening'),

-- Neutral (😐) - Steady and reliable
('phlegmatic', 'maintaining your natural equilibrium', '😐', 'any'),
('phlegmatic', 'providing steady support to others', '😐', 'afternoon'),
('phlegmatic', 'staying grounded and centered', '😐', 'any'),
('phlegmatic', 'approaching the day with calm readiness', '😐', 'morning'),
('phlegmatic', 'keeping things stable and peaceful', '😐', 'afternoon'),
('phlegmatic', 'maintaining your reliable presence', '😐', 'evening'),

-- Sad (😕) - Quietly supportive despite struggles
('phlegmatic', 'finding strength in your gentle nature', '😕', 'any'),
('phlegmatic', 'offering quiet comfort to yourself and others', '😕', 'afternoon'),
('phlegmatic', 'maintaining peace even in difficult times', '😕', 'evening'),
('phlegmatic', 'drawing on your inner resilience', '😕', 'morning'),
('phlegmatic', 'staying steady through the storm', '😕', 'afternoon'),
('phlegmatic', 'trusting in your natural healing ability', '😕', 'evening'),

-- Very Sad (😢) - Seeking and providing comfort
('phlegmatic', 'allowing yourself to rest and heal', '😢', 'any'),
('phlegmatic', 'finding solace in quiet moments', '😢', 'evening'),
('phlegmatic', 'accepting the need for gentle self-care', '😢', 'any'),
('phlegmatic', 'trusting in the comfort of peaceful rest', '😢', 'evening');

-- Create function to get user's latest mood for personalized greetings
CREATE OR REPLACE FUNCTION get_user_latest_mood(user_uuid uuid)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    latest_mood text;
BEGIN
    SELECT emoji INTO latest_mood
    FROM mood_entries
    WHERE user_id = user_uuid
    ORDER BY created_at DESC
    LIMIT 1;
    
    RETURN COALESCE(latest_mood, 'neutral');
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_user_latest_mood(uuid) TO authenticated;